package com.thedasagroup.suminative.ui.orders;

import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class OrderScreenViewModel_Factory_Impl implements OrderScreenViewModel.Factory {
  private final OrderScreenViewModel_Factory delegateFactory;

  OrderScreenViewModel_Factory_Impl(OrderScreenViewModel_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public OrderScreenViewModel create(OrderState state) {
    return delegateFactory.get(state);
  }

  public static Provider<OrderScreenViewModel.Factory> create(
      OrderScreenViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new OrderScreenViewModel_Factory_Impl(delegateFactory));
  }

  public static dagger.internal.Provider<OrderScreenViewModel.Factory> createFactoryProvider(
      OrderScreenViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new OrderScreenViewModel_Factory_Impl(delegateFactory));
  }
}
