package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.LoginRepository;
import com.thedasagroup.suminative.ui.login.LoginUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesLoginUseCaseFactory implements Factory<LoginUseCase> {
  private final Provider<LoginRepository> loginRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  public AppUseCaseModule_ProvidesLoginUseCaseFactory(
      Provider<LoginRepository> loginRepositoryProvider, Provider<Prefs> prefsProvider) {
    this.loginRepositoryProvider = loginRepositoryProvider;
    this.prefsProvider = prefsProvider;
  }

  @Override
  public LoginUseCase get() {
    return providesLoginUseCase(loginRepositoryProvider.get(), prefsProvider.get());
  }

  public static AppUseCaseModule_ProvidesLoginUseCaseFactory create(
      Provider<LoginRepository> loginRepositoryProvider, Provider<Prefs> prefsProvider) {
    return new AppUseCaseModule_ProvidesLoginUseCaseFactory(loginRepositoryProvider, prefsProvider);
  }

  public static LoginUseCase providesLoginUseCase(LoginRepository loginRepository, Prefs prefs) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesLoginUseCase(loginRepository, prefs));
  }
}
