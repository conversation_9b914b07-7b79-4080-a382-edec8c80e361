package com.thedasagroup.suminative.ui.payment;

import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class PaymentViewModel_Factory_Impl implements PaymentViewModel.Factory {
  private final PaymentViewModel_Factory delegateFactory;

  PaymentViewModel_Factory_Impl(PaymentViewModel_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public PaymentViewModel create(PaymentState state) {
    return delegateFactory.get(state);
  }

  public static Provider<PaymentViewModel.Factory> create(
      PaymentViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new PaymentViewModel_Factory_Impl(delegateFactory));
  }

  public static dagger.internal.Provider<PaymentViewModel.Factory> createFactoryProvider(
      PaymentViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new PaymentViewModel_Factory_Impl(delegateFactory));
  }
}
