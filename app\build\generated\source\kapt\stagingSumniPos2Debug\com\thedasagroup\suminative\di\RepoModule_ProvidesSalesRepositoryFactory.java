package com.thedasagroup.suminative.di;

import com.instacart.truetime.time.TrueTimeImpl;
import com.thedasagroup.suminative.data.repo.SalesRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesSalesRepositoryFactory implements Factory<SalesRepository> {
  private final Provider<TrueTimeImpl> trueTimeImplProvider;

  public RepoModule_ProvidesSalesRepositoryFactory(Provider<TrueTimeImpl> trueTimeImplProvider) {
    this.trueTimeImplProvider = trueTimeImplProvider;
  }

  @Override
  public SalesRepository get() {
    return providesSalesRepository(trueTimeImplProvider.get());
  }

  public static RepoModule_ProvidesSalesRepositoryFactory create(
      Provider<TrueTimeImpl> trueTimeImplProvider) {
    return new RepoModule_ProvidesSalesRepositoryFactory(trueTimeImplProvider);
  }

  public static SalesRepository providesSalesRepository(TrueTimeImpl trueTimeImpl) {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesSalesRepository(trueTimeImpl));
  }
}
