package com.thedasagroup.suminative.database

import kotlin.Double
import kotlin.Long
import kotlin.String

public data class OrderEntity(
  public val id: Long,
  public val orderId: String,
  public val customerId: Long?,
  public val customerName: String?,
  public val customerPhone: String?,
  public val orderType: String,
  public val status: String,
  public val subtotal: Double,
  public val tax: Double,
  public val discount: Double,
  public val total: Double,
  public val paymentMethod: String?,
  public val paymentStatus: String,
  public val notes: String?,
  public val tableNumber: Long?,
  public val deliveryAddress: String?,
  public val createdAt: Long,
  public val updatedAt: Long,
  public val completedAt: Long?,
  public val storeId: Long,
  public val waiterId: Long?,
  public val waiterName: String?,
  public val synced: Long,
)
