package com.thedasagroup.suminative.ui.rewards;

import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RewardsViewModel_Factory_Impl implements RewardsViewModel.Factory {
  private final RewardsViewModel_Factory delegateFactory;

  RewardsViewModel_Factory_Impl(RewardsViewModel_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public RewardsViewModel create(RewardsState state) {
    return delegateFactory.get(state);
  }

  public static Provider<RewardsViewModel.Factory> create(
      RewardsViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new RewardsViewModel_Factory_Impl(delegateFactory));
  }

  public static dagger.internal.Provider<RewardsViewModel.Factory> createFactoryProvider(
      RewardsViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new RewardsViewModel_Factory_Impl(delegateFactory));
  }
}
