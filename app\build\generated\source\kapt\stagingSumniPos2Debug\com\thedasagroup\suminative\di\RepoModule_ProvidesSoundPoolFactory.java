package com.thedasagroup.suminative.di;

import android.media.SoundPool;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesSoundPoolFactory implements Factory<SoundPool> {
  @Override
  public SoundPool get() {
    return providesSoundPool();
  }

  public static RepoModule_ProvidesSoundPoolFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SoundPool providesSoundPool() {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesSoundPool());
  }

  private static final class InstanceHolder {
    static final RepoModule_ProvidesSoundPoolFactory INSTANCE = new RepoModule_ProvidesSoundPoolFactory();
  }
}
