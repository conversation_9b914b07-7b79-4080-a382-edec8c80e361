package com.thedasagroup.suminative.ui.local_orders;

import com.thedasagroup.suminative.domain.orders.GetLocalOrdersUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class LocalOrdersViewModel_Factory {
  private final Provider<GetLocalOrdersUseCase> getLocalOrdersUseCaseProvider;

  public LocalOrdersViewModel_Factory(
      Provider<GetLocalOrdersUseCase> getLocalOrdersUseCaseProvider) {
    this.getLocalOrdersUseCaseProvider = getLocalOrdersUseCaseProvider;
  }

  public LocalOrdersViewModel get(LocalOrdersState state) {
    return newInstance(state, getLocalOrdersUseCaseProvider.get());
  }

  public static LocalOrdersViewModel_Factory create(
      Provider<GetLocalOrdersUseCase> getLocalOrdersUseCaseProvider) {
    return new LocalOrdersViewModel_Factory(getLocalOrdersUseCaseProvider);
  }

  public static LocalOrdersViewModel newInstance(LocalOrdersState state,
      GetLocalOrdersUseCase getLocalOrdersUseCase) {
    return new LocalOrdersViewModel(state, getLocalOrdersUseCase);
  }
}
