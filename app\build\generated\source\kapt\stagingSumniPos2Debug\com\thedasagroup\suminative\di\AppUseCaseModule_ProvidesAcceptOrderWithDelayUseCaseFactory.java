package com.thedasagroup.suminative.di;

import com.instacart.truetime.time.TrueTimeImpl;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.OrdersRepository;
import com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase;
import com.thedasagroup.suminative.ui.orders.AcceptOrderWithDelayUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesAcceptOrderWithDelayUseCaseFactory implements Factory<AcceptOrderWithDelayUseCase> {
  private final Provider<Prefs> prefsProvider;

  private final Provider<OrdersRepository> ordersRepositoryProvider;

  private final Provider<TrueTimeImpl> trueTimeImplProvider;

  private final Provider<GetStoreSettingsUseCase> getStoreSettingsUseCaseProvider;

  public AppUseCaseModule_ProvidesAcceptOrderWithDelayUseCaseFactory(Provider<Prefs> prefsProvider,
      Provider<OrdersRepository> ordersRepositoryProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider,
      Provider<GetStoreSettingsUseCase> getStoreSettingsUseCaseProvider) {
    this.prefsProvider = prefsProvider;
    this.ordersRepositoryProvider = ordersRepositoryProvider;
    this.trueTimeImplProvider = trueTimeImplProvider;
    this.getStoreSettingsUseCaseProvider = getStoreSettingsUseCaseProvider;
  }

  @Override
  public AcceptOrderWithDelayUseCase get() {
    return providesAcceptOrderWithDelayUseCase(prefsProvider.get(), ordersRepositoryProvider.get(), trueTimeImplProvider.get(), getStoreSettingsUseCaseProvider.get());
  }

  public static AppUseCaseModule_ProvidesAcceptOrderWithDelayUseCaseFactory create(
      Provider<Prefs> prefsProvider, Provider<OrdersRepository> ordersRepositoryProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider,
      Provider<GetStoreSettingsUseCase> getStoreSettingsUseCaseProvider) {
    return new AppUseCaseModule_ProvidesAcceptOrderWithDelayUseCaseFactory(prefsProvider, ordersRepositoryProvider, trueTimeImplProvider, getStoreSettingsUseCaseProvider);
  }

  public static AcceptOrderWithDelayUseCase providesAcceptOrderWithDelayUseCase(Prefs prefs,
      OrdersRepository ordersRepository, TrueTimeImpl trueTimeImpl,
      GetStoreSettingsUseCase getStoreSettingsUseCase) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesAcceptOrderWithDelayUseCase(prefs, ordersRepository, trueTimeImpl, getStoreSettingsUseCase));
  }
}
