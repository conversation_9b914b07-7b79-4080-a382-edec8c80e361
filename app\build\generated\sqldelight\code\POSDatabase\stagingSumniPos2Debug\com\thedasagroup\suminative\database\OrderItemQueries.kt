package com.thedasagroup.suminative.database

import app.cash.sqldelight.Query
import app.cash.sqldelight.TransacterImpl
import app.cash.sqldelight.db.QueryResult
import app.cash.sqldelight.db.SqlCursor
import app.cash.sqldelight.db.SqlDriver
import kotlin.Any
import kotlin.Double
import kotlin.Long
import kotlin.String

public class OrderItemQueries(
  driver: SqlDriver,
) : TransacterImpl(driver) {
  public fun <T : Any> getOrderItemsByOrderId(orderId: String, mapper: (
    id: Long,
    orderId: String,
    itemId: Long,
    itemName: String,
    itemDescription: String?,
    quantity: Long,
    unitPrice: Double,
    totalPrice: Double,
    category: String?,
    modifiers: String?,
    notes: String?,
    createdAt: Long,
  ) -> T): Query<T> = GetOrderItemsByOrderIdQuery(orderId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2)!!,
      cursor.getString(3)!!,
      cursor.getString(4),
      cursor.getLong(5)!!,
      cursor.getDouble(6)!!,
      cursor.getDouble(7)!!,
      cursor.getString(8),
      cursor.getString(9),
      cursor.getString(10),
      cursor.getLong(11)!!
    )
  }

  public fun getOrderItemsByOrderId(orderId: String): Query<OrderItemEntity> =
      getOrderItemsByOrderId(orderId) { id, orderId_, itemId, itemName, itemDescription, quantity,
      unitPrice, totalPrice, category, modifiers, notes, createdAt ->
    OrderItemEntity(
      id,
      orderId_,
      itemId,
      itemName,
      itemDescription,
      quantity,
      unitPrice,
      totalPrice,
      category,
      modifiers,
      notes,
      createdAt
    )
  }

  public fun <T : Any> getOrderItemById(id: Long, mapper: (
    id: Long,
    orderId: String,
    itemId: Long,
    itemName: String,
    itemDescription: String?,
    quantity: Long,
    unitPrice: Double,
    totalPrice: Double,
    category: String?,
    modifiers: String?,
    notes: String?,
    createdAt: Long,
  ) -> T): Query<T> = GetOrderItemByIdQuery(id) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2)!!,
      cursor.getString(3)!!,
      cursor.getString(4),
      cursor.getLong(5)!!,
      cursor.getDouble(6)!!,
      cursor.getDouble(7)!!,
      cursor.getString(8),
      cursor.getString(9),
      cursor.getString(10),
      cursor.getLong(11)!!
    )
  }

  public fun getOrderItemById(id: Long): Query<OrderItemEntity> = getOrderItemById(id) { id_,
      orderId, itemId, itemName, itemDescription, quantity, unitPrice, totalPrice, category,
      modifiers, notes, createdAt ->
    OrderItemEntity(
      id_,
      orderId,
      itemId,
      itemName,
      itemDescription,
      quantity,
      unitPrice,
      totalPrice,
      category,
      modifiers,
      notes,
      createdAt
    )
  }

  public fun getOrderItemCount(orderId: String): Query<Long> = GetOrderItemCountQuery(orderId) {
      cursor ->
    cursor.getLong(0)!!
  }

  public fun <T : Any> getOrderItemTotal(orderId: String, mapper: (SUM: Double?) -> T): Query<T> =
      GetOrderItemTotalQuery(orderId) { cursor ->
    mapper(
      cursor.getDouble(0)
    )
  }

  public fun getOrderItemTotal(orderId: String): Query<GetOrderItemTotal> =
      getOrderItemTotal(orderId) { SUM ->
    GetOrderItemTotal(
      SUM
    )
  }

  /**
   * @return The number of rows updated.
   */
  public fun insertOrderItem(
    orderId: String,
    itemId: Long,
    itemName: String,
    itemDescription: String?,
    quantity: Long,
    unitPrice: Double,
    totalPrice: Double,
    category: String?,
    modifiers: String?,
    notes: String?,
    createdAt: Long,
  ): QueryResult<Long> {
    val result = driver.execute(10_856_765, """
        |INSERT INTO OrderItemEntity (
        |    orderId, itemId, itemName, itemDescription, quantity, 
        |    unitPrice, totalPrice, category, modifiers, notes, createdAt
        |) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """.trimMargin(), 11) {
          bindString(0, orderId)
          bindLong(1, itemId)
          bindString(2, itemName)
          bindString(3, itemDescription)
          bindLong(4, quantity)
          bindDouble(5, unitPrice)
          bindDouble(6, totalPrice)
          bindString(7, category)
          bindString(8, modifiers)
          bindString(9, notes)
          bindLong(10, createdAt)
        }
    notifyQueries(10_856_765) { emit ->
      emit("OrderItemEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun updateOrderItemQuantity(
    quantity: Long,
    totalPrice: Double,
    modifiers: String?,
    id: Long,
  ): QueryResult<Long> {
    val result = driver.execute(120_017_112, """
        |UPDATE OrderItemEntity 
        |SET quantity = ?, totalPrice = ?, modifiers = ?
        |WHERE id = ?
        """.trimMargin(), 4) {
          bindLong(0, quantity)
          bindDouble(1, totalPrice)
          bindString(2, modifiers)
          bindLong(3, id)
        }
    notifyQueries(120_017_112) { emit ->
      emit("OrderItemEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun deleteOrderItem(id: Long): QueryResult<Long> {
    val result = driver.execute(-1_569_253_749, """DELETE FROM OrderItemEntity WHERE id = ?""", 1) {
          bindLong(0, id)
        }
    notifyQueries(-1_569_253_749) { emit ->
      emit("OrderItemEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun deleteOrderItemsByOrderId(orderId: String): QueryResult<Long> {
    val result = driver.execute(-459_876_790, """DELETE FROM OrderItemEntity WHERE orderId = ?""",
        1) {
          bindString(0, orderId)
        }
    notifyQueries(-459_876_790) { emit ->
      emit("OrderItemEntity")
    }
    return result
  }

  private inner class GetOrderItemsByOrderIdQuery<out T : Any>(
    public val orderId: String,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OrderItemEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OrderItemEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-619_030_923,
        """SELECT OrderItemEntity.id, OrderItemEntity.orderId, OrderItemEntity.itemId, OrderItemEntity.itemName, OrderItemEntity.itemDescription, OrderItemEntity.quantity, OrderItemEntity.unitPrice, OrderItemEntity.totalPrice, OrderItemEntity.category, OrderItemEntity.modifiers, OrderItemEntity.notes, OrderItemEntity.createdAt FROM OrderItemEntity WHERE orderId = ? ORDER BY id ASC""",
        mapper, 1) {
      bindString(0, orderId)
    }

    override fun toString(): String = "OrderItem.sq:getOrderItemsByOrderId"
  }

  private inner class GetOrderItemByIdQuery<out T : Any>(
    public val id: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OrderItemEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OrderItemEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(1_263_035_752,
        """SELECT OrderItemEntity.id, OrderItemEntity.orderId, OrderItemEntity.itemId, OrderItemEntity.itemName, OrderItemEntity.itemDescription, OrderItemEntity.quantity, OrderItemEntity.unitPrice, OrderItemEntity.totalPrice, OrderItemEntity.category, OrderItemEntity.modifiers, OrderItemEntity.notes, OrderItemEntity.createdAt FROM OrderItemEntity WHERE id = ?""",
        mapper, 1) {
      bindLong(0, id)
    }

    override fun toString(): String = "OrderItem.sq:getOrderItemById"
  }

  private inner class GetOrderItemCountQuery<out T : Any>(
    public val orderId: String,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OrderItemEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OrderItemEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(500_070_969,
        """SELECT COUNT(*) FROM OrderItemEntity WHERE orderId = ?""", mapper, 1) {
      bindString(0, orderId)
    }

    override fun toString(): String = "OrderItem.sq:getOrderItemCount"
  }

  private inner class GetOrderItemTotalQuery<out T : Any>(
    public val orderId: String,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OrderItemEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OrderItemEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(515_769_454,
        """SELECT SUM(totalPrice) FROM OrderItemEntity WHERE orderId = ?""", mapper, 1) {
      bindString(0, orderId)
    }

    override fun toString(): String = "OrderItem.sq:getOrderItemTotal"
  }
}
