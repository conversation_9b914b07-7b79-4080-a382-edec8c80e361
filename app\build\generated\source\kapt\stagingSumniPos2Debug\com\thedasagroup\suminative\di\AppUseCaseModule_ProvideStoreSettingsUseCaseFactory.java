package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.LoginRepository;
import com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvideStoreSettingsUseCaseFactory implements Factory<GetStoreSettingsUseCase> {
  private final Provider<LoginRepository> loginRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  public AppUseCaseModule_ProvideStoreSettingsUseCaseFactory(
      Provider<LoginRepository> loginRepositoryProvider, Provider<Prefs> prefsProvider) {
    this.loginRepositoryProvider = loginRepositoryProvider;
    this.prefsProvider = prefsProvider;
  }

  @Override
  public GetStoreSettingsUseCase get() {
    return provideStoreSettingsUseCase(loginRepositoryProvider.get(), prefsProvider.get());
  }

  public static AppUseCaseModule_ProvideStoreSettingsUseCaseFactory create(
      Provider<LoginRepository> loginRepositoryProvider, Provider<Prefs> prefsProvider) {
    return new AppUseCaseModule_ProvideStoreSettingsUseCaseFactory(loginRepositoryProvider, prefsProvider);
  }

  public static GetStoreSettingsUseCase provideStoreSettingsUseCase(LoginRepository loginRepository,
      Prefs prefs) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.provideStoreSettingsUseCase(loginRepository, prefs));
  }
}
