package com.thedasagroup.suminative.database

import app.cash.sqldelight.Query
import app.cash.sqldelight.TransacterImpl
import app.cash.sqldelight.db.QueryResult
import app.cash.sqldelight.db.SqlCursor
import app.cash.sqldelight.db.SqlDriver
import kotlin.Any
import kotlin.Double
import kotlin.Long
import kotlin.String
import kotlin.collections.Collection

public class OrderQueries(
  driver: SqlDriver,
) : TransacterImpl(driver) {
  public fun <T : Any> getAllOrders(mapper: (
    id: Long,
    orderId: String,
    customerId: Long?,
    customerName: String?,
    customerPhone: String?,
    orderType: String,
    status: String,
    subtotal: Double,
    tax: Double,
    discount: Double,
    total: Double,
    paymentMethod: String?,
    paymentStatus: String,
    notes: String?,
    tableNumber: Long?,
    deliveryAddress: String?,
    createdAt: Long,
    updatedAt: Long,
    completedAt: Long?,
    storeId: Long,
    waiterId: Long?,
    waiterName: String?,
    synced: Long,
  ) -> T): Query<T> = Query(1_241_102_504, arrayOf("OrderEntity"), driver, "Order.sq",
      "getAllOrders",
      "SELECT OrderEntity.id, OrderEntity.orderId, OrderEntity.customerId, OrderEntity.customerName, OrderEntity.customerPhone, OrderEntity.orderType, OrderEntity.status, OrderEntity.subtotal, OrderEntity.tax, OrderEntity.discount, OrderEntity.total, OrderEntity.paymentMethod, OrderEntity.paymentStatus, OrderEntity.notes, OrderEntity.tableNumber, OrderEntity.deliveryAddress, OrderEntity.createdAt, OrderEntity.updatedAt, OrderEntity.completedAt, OrderEntity.storeId, OrderEntity.waiterId, OrderEntity.waiterName, OrderEntity.synced FROM OrderEntity ORDER BY createdAt DESC") {
      cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2),
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5)!!,
      cursor.getString(6)!!,
      cursor.getDouble(7)!!,
      cursor.getDouble(8)!!,
      cursor.getDouble(9)!!,
      cursor.getDouble(10)!!,
      cursor.getString(11),
      cursor.getString(12)!!,
      cursor.getString(13),
      cursor.getLong(14),
      cursor.getString(15),
      cursor.getLong(16)!!,
      cursor.getLong(17)!!,
      cursor.getLong(18),
      cursor.getLong(19)!!,
      cursor.getLong(20),
      cursor.getString(21),
      cursor.getLong(22)!!
    )
  }

  public fun getAllOrders(): Query<OrderEntity> = getAllOrders { id, orderId, customerId,
      customerName, customerPhone, orderType, status, subtotal, tax, discount, total, paymentMethod,
      paymentStatus, notes, tableNumber, deliveryAddress, createdAt, updatedAt, completedAt,
      storeId, waiterId, waiterName, synced ->
    OrderEntity(
      id,
      orderId,
      customerId,
      customerName,
      customerPhone,
      orderType,
      status,
      subtotal,
      tax,
      discount,
      total,
      paymentMethod,
      paymentStatus,
      notes,
      tableNumber,
      deliveryAddress,
      createdAt,
      updatedAt,
      completedAt,
      storeId,
      waiterId,
      waiterName,
      synced
    )
  }

  public fun <T : Any> getOrderById(id: Long, mapper: (
    id: Long,
    orderId: String,
    customerId: Long?,
    customerName: String?,
    customerPhone: String?,
    orderType: String,
    status: String,
    subtotal: Double,
    tax: Double,
    discount: Double,
    total: Double,
    paymentMethod: String?,
    paymentStatus: String,
    notes: String?,
    tableNumber: Long?,
    deliveryAddress: String?,
    createdAt: Long,
    updatedAt: Long,
    completedAt: Long?,
    storeId: Long,
    waiterId: Long?,
    waiterName: String?,
    synced: Long,
  ) -> T): Query<T> = GetOrderByIdQuery(id) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2),
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5)!!,
      cursor.getString(6)!!,
      cursor.getDouble(7)!!,
      cursor.getDouble(8)!!,
      cursor.getDouble(9)!!,
      cursor.getDouble(10)!!,
      cursor.getString(11),
      cursor.getString(12)!!,
      cursor.getString(13),
      cursor.getLong(14),
      cursor.getString(15),
      cursor.getLong(16)!!,
      cursor.getLong(17)!!,
      cursor.getLong(18),
      cursor.getLong(19)!!,
      cursor.getLong(20),
      cursor.getString(21),
      cursor.getLong(22)!!
    )
  }

  public fun getOrderById(id: Long): Query<OrderEntity> = getOrderById(id) { id_, orderId,
      customerId, customerName, customerPhone, orderType, status, subtotal, tax, discount, total,
      paymentMethod, paymentStatus, notes, tableNumber, deliveryAddress, createdAt, updatedAt,
      completedAt, storeId, waiterId, waiterName, synced ->
    OrderEntity(
      id_,
      orderId,
      customerId,
      customerName,
      customerPhone,
      orderType,
      status,
      subtotal,
      tax,
      discount,
      total,
      paymentMethod,
      paymentStatus,
      notes,
      tableNumber,
      deliveryAddress,
      createdAt,
      updatedAt,
      completedAt,
      storeId,
      waiterId,
      waiterName,
      synced
    )
  }

  public fun <T : Any> getOrderByOrderId(orderId: String, mapper: (
    id: Long,
    orderId: String,
    customerId: Long?,
    customerName: String?,
    customerPhone: String?,
    orderType: String,
    status: String,
    subtotal: Double,
    tax: Double,
    discount: Double,
    total: Double,
    paymentMethod: String?,
    paymentStatus: String,
    notes: String?,
    tableNumber: Long?,
    deliveryAddress: String?,
    createdAt: Long,
    updatedAt: Long,
    completedAt: Long?,
    storeId: Long,
    waiterId: Long?,
    waiterName: String?,
    synced: Long,
  ) -> T): Query<T> = GetOrderByOrderIdQuery(orderId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2),
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5)!!,
      cursor.getString(6)!!,
      cursor.getDouble(7)!!,
      cursor.getDouble(8)!!,
      cursor.getDouble(9)!!,
      cursor.getDouble(10)!!,
      cursor.getString(11),
      cursor.getString(12)!!,
      cursor.getString(13),
      cursor.getLong(14),
      cursor.getString(15),
      cursor.getLong(16)!!,
      cursor.getLong(17)!!,
      cursor.getLong(18),
      cursor.getLong(19)!!,
      cursor.getLong(20),
      cursor.getString(21),
      cursor.getLong(22)!!
    )
  }

  public fun getOrderByOrderId(orderId: String): Query<OrderEntity> = getOrderByOrderId(orderId) {
      id, orderId_, customerId, customerName, customerPhone, orderType, status, subtotal, tax,
      discount, total, paymentMethod, paymentStatus, notes, tableNumber, deliveryAddress, createdAt,
      updatedAt, completedAt, storeId, waiterId, waiterName, synced ->
    OrderEntity(
      id,
      orderId_,
      customerId,
      customerName,
      customerPhone,
      orderType,
      status,
      subtotal,
      tax,
      discount,
      total,
      paymentMethod,
      paymentStatus,
      notes,
      tableNumber,
      deliveryAddress,
      createdAt,
      updatedAt,
      completedAt,
      storeId,
      waiterId,
      waiterName,
      synced
    )
  }

  public fun <T : Any> getOrdersByStatus(status: String, mapper: (
    id: Long,
    orderId: String,
    customerId: Long?,
    customerName: String?,
    customerPhone: String?,
    orderType: String,
    status: String,
    subtotal: Double,
    tax: Double,
    discount: Double,
    total: Double,
    paymentMethod: String?,
    paymentStatus: String,
    notes: String?,
    tableNumber: Long?,
    deliveryAddress: String?,
    createdAt: Long,
    updatedAt: Long,
    completedAt: Long?,
    storeId: Long,
    waiterId: Long?,
    waiterName: String?,
    synced: Long,
  ) -> T): Query<T> = GetOrdersByStatusQuery(status) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2),
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5)!!,
      cursor.getString(6)!!,
      cursor.getDouble(7)!!,
      cursor.getDouble(8)!!,
      cursor.getDouble(9)!!,
      cursor.getDouble(10)!!,
      cursor.getString(11),
      cursor.getString(12)!!,
      cursor.getString(13),
      cursor.getLong(14),
      cursor.getString(15),
      cursor.getLong(16)!!,
      cursor.getLong(17)!!,
      cursor.getLong(18),
      cursor.getLong(19)!!,
      cursor.getLong(20),
      cursor.getString(21),
      cursor.getLong(22)!!
    )
  }

  public fun getOrdersByStatus(status: String): Query<OrderEntity> = getOrdersByStatus(status) { id,
      orderId, customerId, customerName, customerPhone, orderType, status_, subtotal, tax, discount,
      total, paymentMethod, paymentStatus, notes, tableNumber, deliveryAddress, createdAt,
      updatedAt, completedAt, storeId, waiterId, waiterName, synced ->
    OrderEntity(
      id,
      orderId,
      customerId,
      customerName,
      customerPhone,
      orderType,
      status_,
      subtotal,
      tax,
      discount,
      total,
      paymentMethod,
      paymentStatus,
      notes,
      tableNumber,
      deliveryAddress,
      createdAt,
      updatedAt,
      completedAt,
      storeId,
      waiterId,
      waiterName,
      synced
    )
  }

  public fun <T : Any> getOrdersByStore(storeId: Long, mapper: (
    id: Long,
    orderId: String,
    customerId: Long?,
    customerName: String?,
    customerPhone: String?,
    orderType: String,
    status: String,
    subtotal: Double,
    tax: Double,
    discount: Double,
    total: Double,
    paymentMethod: String?,
    paymentStatus: String,
    notes: String?,
    tableNumber: Long?,
    deliveryAddress: String?,
    createdAt: Long,
    updatedAt: Long,
    completedAt: Long?,
    storeId: Long,
    waiterId: Long?,
    waiterName: String?,
    synced: Long,
  ) -> T): Query<T> = GetOrdersByStoreQuery(storeId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2),
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5)!!,
      cursor.getString(6)!!,
      cursor.getDouble(7)!!,
      cursor.getDouble(8)!!,
      cursor.getDouble(9)!!,
      cursor.getDouble(10)!!,
      cursor.getString(11),
      cursor.getString(12)!!,
      cursor.getString(13),
      cursor.getLong(14),
      cursor.getString(15),
      cursor.getLong(16)!!,
      cursor.getLong(17)!!,
      cursor.getLong(18),
      cursor.getLong(19)!!,
      cursor.getLong(20),
      cursor.getString(21),
      cursor.getLong(22)!!
    )
  }

  public fun getOrdersByStore(storeId: Long): Query<OrderEntity> = getOrdersByStore(storeId) { id,
      orderId, customerId, customerName, customerPhone, orderType, status, subtotal, tax, discount,
      total, paymentMethod, paymentStatus, notes, tableNumber, deliveryAddress, createdAt,
      updatedAt, completedAt, storeId_, waiterId, waiterName, synced ->
    OrderEntity(
      id,
      orderId,
      customerId,
      customerName,
      customerPhone,
      orderType,
      status,
      subtotal,
      tax,
      discount,
      total,
      paymentMethod,
      paymentStatus,
      notes,
      tableNumber,
      deliveryAddress,
      createdAt,
      updatedAt,
      completedAt,
      storeId_,
      waiterId,
      waiterName,
      synced
    )
  }

  public fun <T : Any> getOrdersByDateRange(
    createdAt: Long,
    createdAt_: Long,
    mapper: (
      id: Long,
      orderId: String,
      customerId: Long?,
      customerName: String?,
      customerPhone: String?,
      orderType: String,
      status: String,
      subtotal: Double,
      tax: Double,
      discount: Double,
      total: Double,
      paymentMethod: String?,
      paymentStatus: String,
      notes: String?,
      tableNumber: Long?,
      deliveryAddress: String?,
      createdAt: Long,
      updatedAt: Long,
      completedAt: Long?,
      storeId: Long,
      waiterId: Long?,
      waiterName: String?,
      synced: Long,
    ) -> T,
  ): Query<T> = GetOrdersByDateRangeQuery(createdAt, createdAt_) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2),
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5)!!,
      cursor.getString(6)!!,
      cursor.getDouble(7)!!,
      cursor.getDouble(8)!!,
      cursor.getDouble(9)!!,
      cursor.getDouble(10)!!,
      cursor.getString(11),
      cursor.getString(12)!!,
      cursor.getString(13),
      cursor.getLong(14),
      cursor.getString(15),
      cursor.getLong(16)!!,
      cursor.getLong(17)!!,
      cursor.getLong(18),
      cursor.getLong(19)!!,
      cursor.getLong(20),
      cursor.getString(21),
      cursor.getLong(22)!!
    )
  }

  public fun getOrdersByDateRange(createdAt: Long, createdAt_: Long): Query<OrderEntity> =
      getOrdersByDateRange(createdAt, createdAt_) { id, orderId, customerId, customerName,
      customerPhone, orderType, status, subtotal, tax, discount, total, paymentMethod,
      paymentStatus, notes, tableNumber, deliveryAddress, createdAt__, updatedAt, completedAt,
      storeId, waiterId, waiterName, synced ->
    OrderEntity(
      id,
      orderId,
      customerId,
      customerName,
      customerPhone,
      orderType,
      status,
      subtotal,
      tax,
      discount,
      total,
      paymentMethod,
      paymentStatus,
      notes,
      tableNumber,
      deliveryAddress,
      createdAt__,
      updatedAt,
      completedAt,
      storeId,
      waiterId,
      waiterName,
      synced
    )
  }

  public fun <T : Any> getPendingOrders(mapper: (
    id: Long,
    orderId: String,
    customerId: Long?,
    customerName: String?,
    customerPhone: String?,
    orderType: String,
    status: String,
    subtotal: Double,
    tax: Double,
    discount: Double,
    total: Double,
    paymentMethod: String?,
    paymentStatus: String,
    notes: String?,
    tableNumber: Long?,
    deliveryAddress: String?,
    createdAt: Long,
    updatedAt: Long,
    completedAt: Long?,
    storeId: Long,
    waiterId: Long?,
    waiterName: String?,
    synced: Long,
  ) -> T): Query<T> = Query(-841_595_106, arrayOf("OrderEntity"), driver, "Order.sq",
      "getPendingOrders", """
  |SELECT OrderEntity.id, OrderEntity.orderId, OrderEntity.customerId, OrderEntity.customerName, OrderEntity.customerPhone, OrderEntity.orderType, OrderEntity.status, OrderEntity.subtotal, OrderEntity.tax, OrderEntity.discount, OrderEntity.total, OrderEntity.paymentMethod, OrderEntity.paymentStatus, OrderEntity.notes, OrderEntity.tableNumber, OrderEntity.deliveryAddress, OrderEntity.createdAt, OrderEntity.updatedAt, OrderEntity.completedAt, OrderEntity.storeId, OrderEntity.waiterId, OrderEntity.waiterName, OrderEntity.synced FROM OrderEntity 
  |WHERE status IN ('PENDING', 'ACCEPTED', 'PREPARING') 
  |ORDER BY createdAt ASC
  """.trimMargin()) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2),
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5)!!,
      cursor.getString(6)!!,
      cursor.getDouble(7)!!,
      cursor.getDouble(8)!!,
      cursor.getDouble(9)!!,
      cursor.getDouble(10)!!,
      cursor.getString(11),
      cursor.getString(12)!!,
      cursor.getString(13),
      cursor.getLong(14),
      cursor.getString(15),
      cursor.getLong(16)!!,
      cursor.getLong(17)!!,
      cursor.getLong(18),
      cursor.getLong(19)!!,
      cursor.getLong(20),
      cursor.getString(21),
      cursor.getLong(22)!!
    )
  }

  public fun getPendingOrders(): Query<OrderEntity> = getPendingOrders { id, orderId, customerId,
      customerName, customerPhone, orderType, status, subtotal, tax, discount, total, paymentMethod,
      paymentStatus, notes, tableNumber, deliveryAddress, createdAt, updatedAt, completedAt,
      storeId, waiterId, waiterName, synced ->
    OrderEntity(
      id,
      orderId,
      customerId,
      customerName,
      customerPhone,
      orderType,
      status,
      subtotal,
      tax,
      discount,
      total,
      paymentMethod,
      paymentStatus,
      notes,
      tableNumber,
      deliveryAddress,
      createdAt,
      updatedAt,
      completedAt,
      storeId,
      waiterId,
      waiterName,
      synced
    )
  }

  public fun <T : Any> getTodaysOrders(
    createdAt: Long,
    storeId: Long,
    mapper: (
      id: Long,
      orderId: String,
      customerId: Long?,
      customerName: String?,
      customerPhone: String?,
      orderType: String,
      status: String,
      subtotal: Double,
      tax: Double,
      discount: Double,
      total: Double,
      paymentMethod: String?,
      paymentStatus: String,
      notes: String?,
      tableNumber: Long?,
      deliveryAddress: String?,
      createdAt: Long,
      updatedAt: Long,
      completedAt: Long?,
      storeId: Long,
      waiterId: Long?,
      waiterName: String?,
      synced: Long,
    ) -> T,
  ): Query<T> = GetTodaysOrdersQuery(createdAt, storeId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2),
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5)!!,
      cursor.getString(6)!!,
      cursor.getDouble(7)!!,
      cursor.getDouble(8)!!,
      cursor.getDouble(9)!!,
      cursor.getDouble(10)!!,
      cursor.getString(11),
      cursor.getString(12)!!,
      cursor.getString(13),
      cursor.getLong(14),
      cursor.getString(15),
      cursor.getLong(16)!!,
      cursor.getLong(17)!!,
      cursor.getLong(18),
      cursor.getLong(19)!!,
      cursor.getLong(20),
      cursor.getString(21),
      cursor.getLong(22)!!
    )
  }

  public fun getTodaysOrders(createdAt: Long, storeId: Long): Query<OrderEntity> =
      getTodaysOrders(createdAt, storeId) { id, orderId, customerId, customerName, customerPhone,
      orderType, status, subtotal, tax, discount, total, paymentMethod, paymentStatus, notes,
      tableNumber, deliveryAddress, createdAt_, updatedAt, completedAt, storeId_, waiterId,
      waiterName, synced ->
    OrderEntity(
      id,
      orderId,
      customerId,
      customerName,
      customerPhone,
      orderType,
      status,
      subtotal,
      tax,
      discount,
      total,
      paymentMethod,
      paymentStatus,
      notes,
      tableNumber,
      deliveryAddress,
      createdAt_,
      updatedAt,
      completedAt,
      storeId_,
      waiterId,
      waiterName,
      synced
    )
  }

  public fun <T : Any> getUnsyncedOrders(mapper: (
    id: Long,
    orderId: String,
    customerId: Long?,
    customerName: String?,
    customerPhone: String?,
    orderType: String,
    status: String,
    subtotal: Double,
    tax: Double,
    discount: Double,
    total: Double,
    paymentMethod: String?,
    paymentStatus: String,
    notes: String?,
    tableNumber: Long?,
    deliveryAddress: String?,
    createdAt: Long,
    updatedAt: Long,
    completedAt: Long?,
    storeId: Long,
    waiterId: Long?,
    waiterName: String?,
    synced: Long,
  ) -> T): Query<T> = Query(-926_440_042, arrayOf("OrderEntity"), driver, "Order.sq",
      "getUnsyncedOrders", """
  |SELECT OrderEntity.id, OrderEntity.orderId, OrderEntity.customerId, OrderEntity.customerName, OrderEntity.customerPhone, OrderEntity.orderType, OrderEntity.status, OrderEntity.subtotal, OrderEntity.tax, OrderEntity.discount, OrderEntity.total, OrderEntity.paymentMethod, OrderEntity.paymentStatus, OrderEntity.notes, OrderEntity.tableNumber, OrderEntity.deliveryAddress, OrderEntity.createdAt, OrderEntity.updatedAt, OrderEntity.completedAt, OrderEntity.storeId, OrderEntity.waiterId, OrderEntity.waiterName, OrderEntity.synced FROM OrderEntity 
  |WHERE synced = 0 
  |ORDER BY createdAt ASC
  """.trimMargin()) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2),
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5)!!,
      cursor.getString(6)!!,
      cursor.getDouble(7)!!,
      cursor.getDouble(8)!!,
      cursor.getDouble(9)!!,
      cursor.getDouble(10)!!,
      cursor.getString(11),
      cursor.getString(12)!!,
      cursor.getString(13),
      cursor.getLong(14),
      cursor.getString(15),
      cursor.getLong(16)!!,
      cursor.getLong(17)!!,
      cursor.getLong(18),
      cursor.getLong(19)!!,
      cursor.getLong(20),
      cursor.getString(21),
      cursor.getLong(22)!!
    )
  }

  public fun getUnsyncedOrders(): Query<OrderEntity> = getUnsyncedOrders { id, orderId, customerId,
      customerName, customerPhone, orderType, status, subtotal, tax, discount, total, paymentMethod,
      paymentStatus, notes, tableNumber, deliveryAddress, createdAt, updatedAt, completedAt,
      storeId, waiterId, waiterName, synced ->
    OrderEntity(
      id,
      orderId,
      customerId,
      customerName,
      customerPhone,
      orderType,
      status,
      subtotal,
      tax,
      discount,
      total,
      paymentMethod,
      paymentStatus,
      notes,
      tableNumber,
      deliveryAddress,
      createdAt,
      updatedAt,
      completedAt,
      storeId,
      waiterId,
      waiterName,
      synced
    )
  }

  public fun <T : Any> getSyncedOrders(mapper: (
    id: Long,
    orderId: String,
    customerId: Long?,
    customerName: String?,
    customerPhone: String?,
    orderType: String,
    status: String,
    subtotal: Double,
    tax: Double,
    discount: Double,
    total: Double,
    paymentMethod: String?,
    paymentStatus: String,
    notes: String?,
    tableNumber: Long?,
    deliveryAddress: String?,
    createdAt: Long,
    updatedAt: Long,
    completedAt: Long?,
    storeId: Long,
    waiterId: Long?,
    waiterName: String?,
    synced: Long,
  ) -> T): Query<T> = Query(-950_092_803, arrayOf("OrderEntity"), driver, "Order.sq",
      "getSyncedOrders", """
  |SELECT OrderEntity.id, OrderEntity.orderId, OrderEntity.customerId, OrderEntity.customerName, OrderEntity.customerPhone, OrderEntity.orderType, OrderEntity.status, OrderEntity.subtotal, OrderEntity.tax, OrderEntity.discount, OrderEntity.total, OrderEntity.paymentMethod, OrderEntity.paymentStatus, OrderEntity.notes, OrderEntity.tableNumber, OrderEntity.deliveryAddress, OrderEntity.createdAt, OrderEntity.updatedAt, OrderEntity.completedAt, OrderEntity.storeId, OrderEntity.waiterId, OrderEntity.waiterName, OrderEntity.synced FROM OrderEntity 
  |WHERE synced = 1 
  |ORDER BY createdAt DESC
  """.trimMargin()) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2),
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5)!!,
      cursor.getString(6)!!,
      cursor.getDouble(7)!!,
      cursor.getDouble(8)!!,
      cursor.getDouble(9)!!,
      cursor.getDouble(10)!!,
      cursor.getString(11),
      cursor.getString(12)!!,
      cursor.getString(13),
      cursor.getLong(14),
      cursor.getString(15),
      cursor.getLong(16)!!,
      cursor.getLong(17)!!,
      cursor.getLong(18),
      cursor.getLong(19)!!,
      cursor.getLong(20),
      cursor.getString(21),
      cursor.getLong(22)!!
    )
  }

  public fun getSyncedOrders(): Query<OrderEntity> = getSyncedOrders { id, orderId, customerId,
      customerName, customerPhone, orderType, status, subtotal, tax, discount, total, paymentMethod,
      paymentStatus, notes, tableNumber, deliveryAddress, createdAt, updatedAt, completedAt,
      storeId, waiterId, waiterName, synced ->
    OrderEntity(
      id,
      orderId,
      customerId,
      customerName,
      customerPhone,
      orderType,
      status,
      subtotal,
      tax,
      discount,
      total,
      paymentMethod,
      paymentStatus,
      notes,
      tableNumber,
      deliveryAddress,
      createdAt,
      updatedAt,
      completedAt,
      storeId,
      waiterId,
      waiterName,
      synced
    )
  }

  public fun getOrderCount(): Query<Long> = Query(365_913_567, arrayOf("OrderEntity"), driver,
      "Order.sq", "getOrderCount", "SELECT COUNT(*) FROM OrderEntity") { cursor ->
    cursor.getLong(0)!!
  }

  public fun getUnsyncedOrderCount(): Query<Long> = Query(675_200_146, arrayOf("OrderEntity"),
      driver, "Order.sq", "getUnsyncedOrderCount",
      "SELECT COUNT(*) FROM OrderEntity WHERE synced = 0") { cursor ->
    cursor.getLong(0)!!
  }

  public fun <T : Any> getTotalSalesByDateRange(
    createdAt: Long,
    createdAt_: Long,
    storeId: Long,
    mapper: (SUM: Double?) -> T,
  ): Query<T> = GetTotalSalesByDateRangeQuery(createdAt, createdAt_, storeId) { cursor ->
    mapper(
      cursor.getDouble(0)
    )
  }

  public fun getTotalSalesByDateRange(
    createdAt: Long,
    createdAt_: Long,
    storeId: Long,
  ): Query<GetTotalSalesByDateRange> = getTotalSalesByDateRange(createdAt, createdAt_, storeId) {
      SUM ->
    GetTotalSalesByDateRange(
      SUM
    )
  }

  /**
   * @return The number of rows updated.
   */
  public fun insertOrder(
    orderId: String,
    customerId: Long?,
    customerName: String?,
    customerPhone: String?,
    orderType: String,
    status: String,
    subtotal: Double,
    tax: Double,
    discount: Double,
    total: Double,
    paymentMethod: String?,
    paymentStatus: String,
    notes: String?,
    tableNumber: Long?,
    deliveryAddress: String?,
    createdAt: Long,
    updatedAt: Long,
    storeId: Long,
    waiterId: Long?,
    waiterName: String?,
    synced: Long,
  ): QueryResult<Long> {
    val result = driver.execute(-1_722_526_307, """
        |INSERT INTO OrderEntity (
        |    orderId, customerId, customerName, customerPhone, orderType, status,
        |    subtotal, tax, discount, total, paymentMethod, paymentStatus,
        |    notes, tableNumber, deliveryAddress, createdAt, updatedAt,
        |    storeId, waiterId, waiterName, synced
        |) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """.trimMargin(), 21) {
          bindString(0, orderId)
          bindLong(1, customerId)
          bindString(2, customerName)
          bindString(3, customerPhone)
          bindString(4, orderType)
          bindString(5, status)
          bindDouble(6, subtotal)
          bindDouble(7, tax)
          bindDouble(8, discount)
          bindDouble(9, total)
          bindString(10, paymentMethod)
          bindString(11, paymentStatus)
          bindString(12, notes)
          bindLong(13, tableNumber)
          bindString(14, deliveryAddress)
          bindLong(15, createdAt)
          bindLong(16, updatedAt)
          bindLong(17, storeId)
          bindLong(18, waiterId)
          bindString(19, waiterName)
          bindLong(20, synced)
        }
    notifyQueries(-1_722_526_307) { emit ->
      emit("OrderEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun updateOrderStatus(
    status: String,
    updatedAt: Long,
    id: Long,
  ): QueryResult<Long> {
    val result = driver.execute(-355_809_313, """
        |UPDATE OrderEntity 
        |SET status = ?, updatedAt = ?
        |WHERE id = ?
        """.trimMargin(), 3) {
          bindString(0, status)
          bindLong(1, updatedAt)
          bindLong(2, id)
        }
    notifyQueries(-355_809_313) { emit ->
      emit("OrderEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun updateOrderPayment(
    paymentMethod: String?,
    paymentStatus: String,
    updatedAt: Long,
    id: Long,
  ): QueryResult<Long> {
    val result = driver.execute(-1_329_711_175, """
        |UPDATE OrderEntity 
        |SET paymentMethod = ?, paymentStatus = ?, updatedAt = ?
        |WHERE id = ?
        """.trimMargin(), 4) {
          bindString(0, paymentMethod)
          bindString(1, paymentStatus)
          bindLong(2, updatedAt)
          bindLong(3, id)
        }
    notifyQueries(-1_329_711_175) { emit ->
      emit("OrderEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun markOrderComplete(
    completedAt: Long?,
    updatedAt: Long,
    id: Long,
  ): QueryResult<Long> {
    val result = driver.execute(-2_113_618_846, """
        |UPDATE OrderEntity 
        |SET status = 'COMPLETED', completedAt = ?, updatedAt = ?
        |WHERE id = ?
        """.trimMargin(), 3) {
          bindLong(0, completedAt)
          bindLong(1, updatedAt)
          bindLong(2, id)
        }
    notifyQueries(-2_113_618_846) { emit ->
      emit("OrderEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun markOrderSynced(updatedAt: Long, id: Long): QueryResult<Long> {
    val result = driver.execute(442_772_963, """
        |UPDATE OrderEntity 
        |SET synced = 1, updatedAt = ?
        |WHERE id = ?
        """.trimMargin(), 2) {
          bindLong(0, updatedAt)
          bindLong(1, id)
        }
    notifyQueries(442_772_963) { emit ->
      emit("OrderEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun markOrderUnsynced(updatedAt: Long, id: Long): QueryResult<Long> {
    val result = driver.execute(-1_515_163_780, """
        |UPDATE OrderEntity 
        |SET synced = 0, updatedAt = ?
        |WHERE id = ?
        """.trimMargin(), 2) {
          bindLong(0, updatedAt)
          bindLong(1, id)
        }
    notifyQueries(-1_515_163_780) { emit ->
      emit("OrderEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun markOrdersSyncedByOrderIds(updatedAt: Long, orderId: Collection<String>):
      QueryResult<Long> {
    val orderIdIndexes = createArguments(count = orderId.size)
    val result = driver.execute(null, """
        |UPDATE OrderEntity 
        |SET synced = 1, updatedAt = ?
        |WHERE orderId IN $orderIdIndexes
        """.trimMargin(), 1 + orderId.size) {
          bindLong(0, updatedAt)
          orderId.forEachIndexed { index, orderId_ ->
            bindString(index + 1, orderId_)
          }
        }
    notifyQueries(653_426_629) { emit ->
      emit("OrderEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun deleteOrder(id: Long): QueryResult<Long> {
    val result = driver.execute(1_553_766_891, """DELETE FROM OrderEntity WHERE id = ?""", 1) {
          bindLong(0, id)
        }
    notifyQueries(1_553_766_891) { emit ->
      emit("OrderEntity")
      emit("OrderItemEntity")
    }
    return result
  }

  private inner class GetOrderByIdQuery<out T : Any>(
    public val id: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OrderEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OrderEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(1_397_255_426,
        """SELECT OrderEntity.id, OrderEntity.orderId, OrderEntity.customerId, OrderEntity.customerName, OrderEntity.customerPhone, OrderEntity.orderType, OrderEntity.status, OrderEntity.subtotal, OrderEntity.tax, OrderEntity.discount, OrderEntity.total, OrderEntity.paymentMethod, OrderEntity.paymentStatus, OrderEntity.notes, OrderEntity.tableNumber, OrderEntity.deliveryAddress, OrderEntity.createdAt, OrderEntity.updatedAt, OrderEntity.completedAt, OrderEntity.storeId, OrderEntity.waiterId, OrderEntity.waiterName, OrderEntity.synced FROM OrderEntity WHERE id = ?""",
        mapper, 1) {
      bindLong(0, id)
    }

    override fun toString(): String = "Order.sq:getOrderById"
  }

  private inner class GetOrderByOrderIdQuery<out T : Any>(
    public val orderId: String,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OrderEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OrderEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-665_358_974,
        """SELECT OrderEntity.id, OrderEntity.orderId, OrderEntity.customerId, OrderEntity.customerName, OrderEntity.customerPhone, OrderEntity.orderType, OrderEntity.status, OrderEntity.subtotal, OrderEntity.tax, OrderEntity.discount, OrderEntity.total, OrderEntity.paymentMethod, OrderEntity.paymentStatus, OrderEntity.notes, OrderEntity.tableNumber, OrderEntity.deliveryAddress, OrderEntity.createdAt, OrderEntity.updatedAt, OrderEntity.completedAt, OrderEntity.storeId, OrderEntity.waiterId, OrderEntity.waiterName, OrderEntity.synced FROM OrderEntity WHERE orderId = ?""",
        mapper, 1) {
      bindString(0, orderId)
    }

    override fun toString(): String = "Order.sq:getOrderByOrderId"
  }

  private inner class GetOrdersByStatusQuery<out T : Any>(
    public val status: String,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OrderEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OrderEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(1_640_970_668,
        """SELECT OrderEntity.id, OrderEntity.orderId, OrderEntity.customerId, OrderEntity.customerName, OrderEntity.customerPhone, OrderEntity.orderType, OrderEntity.status, OrderEntity.subtotal, OrderEntity.tax, OrderEntity.discount, OrderEntity.total, OrderEntity.paymentMethod, OrderEntity.paymentStatus, OrderEntity.notes, OrderEntity.tableNumber, OrderEntity.deliveryAddress, OrderEntity.createdAt, OrderEntity.updatedAt, OrderEntity.completedAt, OrderEntity.storeId, OrderEntity.waiterId, OrderEntity.waiterName, OrderEntity.synced FROM OrderEntity WHERE status = ? ORDER BY createdAt DESC""",
        mapper, 1) {
      bindString(0, status)
    }

    override fun toString(): String = "Order.sq:getOrdersByStatus"
  }

  private inner class GetOrdersByStoreQuery<out T : Any>(
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OrderEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OrderEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(1_161_326_567,
        """SELECT OrderEntity.id, OrderEntity.orderId, OrderEntity.customerId, OrderEntity.customerName, OrderEntity.customerPhone, OrderEntity.orderType, OrderEntity.status, OrderEntity.subtotal, OrderEntity.tax, OrderEntity.discount, OrderEntity.total, OrderEntity.paymentMethod, OrderEntity.paymentStatus, OrderEntity.notes, OrderEntity.tableNumber, OrderEntity.deliveryAddress, OrderEntity.createdAt, OrderEntity.updatedAt, OrderEntity.completedAt, OrderEntity.storeId, OrderEntity.waiterId, OrderEntity.waiterName, OrderEntity.synced FROM OrderEntity WHERE storeId = ? ORDER BY createdAt DESC""",
        mapper, 1) {
      bindLong(0, storeId)
    }

    override fun toString(): String = "Order.sq:getOrdersByStore"
  }

  private inner class GetOrdersByDateRangeQuery<out T : Any>(
    public val createdAt: Long,
    public val createdAt_: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OrderEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OrderEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-1_646_691_787, """
    |SELECT OrderEntity.id, OrderEntity.orderId, OrderEntity.customerId, OrderEntity.customerName, OrderEntity.customerPhone, OrderEntity.orderType, OrderEntity.status, OrderEntity.subtotal, OrderEntity.tax, OrderEntity.discount, OrderEntity.total, OrderEntity.paymentMethod, OrderEntity.paymentStatus, OrderEntity.notes, OrderEntity.tableNumber, OrderEntity.deliveryAddress, OrderEntity.createdAt, OrderEntity.updatedAt, OrderEntity.completedAt, OrderEntity.storeId, OrderEntity.waiterId, OrderEntity.waiterName, OrderEntity.synced FROM OrderEntity 
    |WHERE createdAt >= ? AND createdAt <= ? 
    |ORDER BY createdAt DESC
    """.trimMargin(), mapper, 2) {
      bindLong(0, createdAt)
      bindLong(1, createdAt_)
    }

    override fun toString(): String = "Order.sq:getOrdersByDateRange"
  }

  private inner class GetTodaysOrdersQuery<out T : Any>(
    public val createdAt: Long,
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OrderEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OrderEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-2_117_134_635, """
    |SELECT OrderEntity.id, OrderEntity.orderId, OrderEntity.customerId, OrderEntity.customerName, OrderEntity.customerPhone, OrderEntity.orderType, OrderEntity.status, OrderEntity.subtotal, OrderEntity.tax, OrderEntity.discount, OrderEntity.total, OrderEntity.paymentMethod, OrderEntity.paymentStatus, OrderEntity.notes, OrderEntity.tableNumber, OrderEntity.deliveryAddress, OrderEntity.createdAt, OrderEntity.updatedAt, OrderEntity.completedAt, OrderEntity.storeId, OrderEntity.waiterId, OrderEntity.waiterName, OrderEntity.synced FROM OrderEntity 
    |WHERE createdAt >= ? AND storeId = ?
    |ORDER BY createdAt DESC
    """.trimMargin(), mapper, 2) {
      bindLong(0, createdAt)
      bindLong(1, storeId)
    }

    override fun toString(): String = "Order.sq:getTodaysOrders"
  }

  private inner class GetTotalSalesByDateRangeQuery<out T : Any>(
    public val createdAt: Long,
    public val createdAt_: Long,
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OrderEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OrderEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-824_710_126, """
    |SELECT SUM(total) FROM OrderEntity 
    |WHERE createdAt >= ? AND createdAt <= ? 
    |AND paymentStatus = 'PAID' AND storeId = ?
    """.trimMargin(), mapper, 3) {
      bindLong(0, createdAt)
      bindLong(1, createdAt_)
      bindLong(2, storeId)
    }

    override fun toString(): String = "Order.sq:getTotalSalesByDateRange"
  }
}
