package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.StockRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesStockRepositoryFactory implements Factory<StockRepository> {
  @Override
  public StockRepository get() {
    return providesStockRepository();
  }

  public static RepoModule_ProvidesStockRepositoryFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static StockRepository providesStockRepository() {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesStockRepository());
  }

  private static final class InstanceHolder {
    static final RepoModule_ProvidesStockRepositoryFactory INSTANCE = new RepoModule_ProvidesStockRepositoryFactory();
  }
}
