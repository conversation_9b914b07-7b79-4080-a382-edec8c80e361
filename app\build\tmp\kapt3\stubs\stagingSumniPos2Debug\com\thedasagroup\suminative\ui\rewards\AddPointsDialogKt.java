package com.thedasagroup.suminative.ui.rewards;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\"\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a&\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00040\nH\u0007\"\u0010\u0010\u0000\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0004\n\u0002\u0010\u0002\u00a8\u0006\u000b"}, d2 = {"ThemeGreen", "Landroidx/compose/ui/graphics/Color;", "J", "AddPointsDialog", "", "customer", "Lcom/thedasagroup/suminative/data/model/response/rewards/RewardsCustomer;", "viewModel", "Lcom/thedasagroup/suminative/ui/rewards/RewardsViewModel;", "onDismiss", "Lkotlin/Function0;", "app_stagingSumniPos2Debug"})
public final class AddPointsDialogKt {
    private static final long ThemeGreen = 0L;
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AddPointsDialog(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer customer, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.rewards.RewardsViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
}