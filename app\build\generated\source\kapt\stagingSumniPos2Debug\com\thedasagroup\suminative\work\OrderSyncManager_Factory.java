package com.thedasagroup.suminative.work;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class OrderSyncManager_Factory implements Factory<OrderSyncManager> {
  private final Provider<Context> contextProvider;

  public OrderSyncManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public OrderSyncManager get() {
    return newInstance(contextProvider.get());
  }

  public static OrderSyncManager_Factory create(Provider<Context> contextProvider) {
    return new OrderSyncManager_Factory(contextProvider);
  }

  public static OrderSyncManager newInstance(Context context) {
    return new OrderSyncManager(context);
  }
}
