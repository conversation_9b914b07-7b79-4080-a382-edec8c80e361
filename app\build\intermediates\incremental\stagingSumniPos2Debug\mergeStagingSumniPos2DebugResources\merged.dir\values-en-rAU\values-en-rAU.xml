<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="urn:oasis:names:tc:xliff:document:1.2">
    <string msgid="5976598919945601918" name="abc_action_bar_home_description">"Navigate home"</string>
    <string msgid="8388173803310557296" name="abc_action_bar_up_description">"Navigate up"</string>
    <string msgid="3937310113216875497" name="abc_action_menu_overflow_description">"More options"</string>
    <string msgid="4692188335987374352" name="abc_action_mode_done">"Done"</string>
    <string msgid="1189761859438369441" name="abc_activity_chooser_view_see_all">"See all"</string>
    <string msgid="2165779757652331008" name="abc_activitychooserview_choose_application">"Choose an app"</string>
    <string msgid="4215997306490295099" name="abc_capital_off">"OFF"</string>
    <string msgid="884982626291842264" name="abc_capital_on">"ON"</string>
    <string msgid="8833365367933412986" name="abc_menu_alt_shortcut_label">"Alt+"</string>
    <string msgid="2223301931652355242" name="abc_menu_ctrl_shortcut_label">"Ctrl+"</string>
    <string msgid="838001238306846836" name="abc_menu_delete_shortcut_label">"delete"</string>
    <string msgid="7986526966204849475" name="abc_menu_enter_shortcut_label">"enter"</string>
    <string msgid="375214403600139847" name="abc_menu_function_shortcut_label">"Function+"</string>
    <string msgid="4192209724446364286" name="abc_menu_meta_shortcut_label">"Meta+"</string>
    <string msgid="4741552369836443843" name="abc_menu_shift_shortcut_label">"Shift+"</string>
    <string msgid="5473865519181928982" name="abc_menu_space_shortcut_label">"space"</string>
    <string msgid="6180552449598693998" name="abc_menu_sym_shortcut_label">"Sym+"</string>
    <string msgid="5520303668377388990" name="abc_prepend_shortcut_label">"Menu+"</string>
    <string msgid="7208076849092622260" name="abc_search_hint">"Search…"</string>
    <string msgid="3741173234950517107" name="abc_searchview_description_clear">"Clear query"</string>
    <string msgid="693312494995508443" name="abc_searchview_description_query">"Search query"</string>
    <string msgid="3417662926640357176" name="abc_searchview_description_search">"Search"</string>
    <string msgid="1486535517437947103" name="abc_searchview_description_submit">"Submit query"</string>
    <string msgid="2293578557972875415" name="abc_searchview_description_voice">"Voice search"</string>
    <string msgid="8875138169939072951" name="abc_shareactionprovider_share_with">"Share with"</string>
    <string msgid="9055268688411532828" name="abc_shareactionprovider_share_with_application">"Share with <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string>
    <string msgid="1656852541809559762" name="abc_toolbar_collapse_description">"Collapse"</string>
    <string msgid="881409763997275156" name="call_notification_answer_action">"Answer"</string>
    <string msgid="8793775615905189152" name="call_notification_answer_video_action">"Video"</string>
    <string msgid="3229508546291798546" name="call_notification_decline_action">"Decline"</string>
    <string msgid="2659457946726154263" name="call_notification_hang_up_action">"Hang up"</string>
    <string msgid="6107532579223922871" name="call_notification_incoming_text">"Incoming call"</string>
    <string msgid="8623827134497363134" name="call_notification_ongoing_text">"On-going call"</string>
    <string msgid="59049573811482460" name="call_notification_screening_text">"Screening an incoming call"</string>
    <string msgid="406453423630273620" name="close_drawer">"Close navigation menu"</string>
    <string msgid="7573152094250666567" name="close_sheet">"Close sheet"</string>
    <string msgid="3260749812566568062" name="copy_toast_msg">"Link copied to clipboard"</string>
    <string msgid="8038256446254964252" name="default_error_message">"Invalid input"</string>
    <string msgid="6312721426453364202" name="default_popup_window_title">"Pop-up window"</string>
    <string msgid="1890207353314751437" name="dropdown_menu">"Drop-down menu"</string>
    <string msgid="4566929209979330987" name="fallback_menu_item_copy_link">"Copy link"</string>
    <string msgid="3413186855122069269" name="fallback_menu_item_open_in_browser">"Open in browser"</string>
    <string msgid="7145444925855055364" name="fallback_menu_item_share_link">"Share link"</string>
    <string msgid="6827826412747255547" name="in_progress">"In progress"</string>
    <string msgid="7933458017204019916" name="indeterminate">"Partially ticked"</string>
    <string msgid="2988463736136100848" name="m3c_bottom_sheet_collapse_description">"Collapse bottom sheet"</string>
    <string msgid="1555567894577437024" name="m3c_bottom_sheet_dismiss_description">"Dismiss bottom sheet"</string>
    <string msgid="8403354765404029791" name="m3c_bottom_sheet_drag_handle_description">"Drag handle"</string>
    <string msgid="6670819569745899763" name="m3c_bottom_sheet_expand_description">"Expand bottom sheet"</string>
    <string msgid="3010635850035863127" name="m3c_bottom_sheet_pane_title">"Bottom sheet"</string>
    <string msgid="8166741421776570875" name="m3c_date_input_headline">"Entered date"</string>
    <string msgid="229313757840775812" name="m3c_date_input_headline_description">"Entered date: %1$s"</string>
    <string msgid="6116910750161463197" name="m3c_date_input_invalid_for_pattern">"Date does not match expected pattern: %1$s"</string>
    <string msgid="2521768508935305279" name="m3c_date_input_invalid_not_allowed">"Date not allowed: %1$s"</string>
    <string msgid="7052898923934555305" name="m3c_date_input_invalid_year_range">"Date out of expected year range %1$s - %2$s"</string>
    <string msgid="2895559812010326913" name="m3c_date_input_label">"Date"</string>
    <string msgid="1237013946323089826" name="m3c_date_input_no_input_description">"None"</string>
    <string msgid="7306227249789210568" name="m3c_date_input_title">"Select date"</string>
    <string msgid="7605002211875882969" name="m3c_date_picker_headline">"Selected date"</string>
    <string msgid="3664277305226978227" name="m3c_date_picker_headline_description">"Current selection: %1$s"</string>
    <string msgid="8436650776581492840" name="m3c_date_picker_navigate_to_year_description">"Navigate to year %1$s"</string>
    <string msgid="5811000998184572395" name="m3c_date_picker_no_selection_description">"None"</string>
    <string msgid="7813882352367152251" name="m3c_date_picker_scroll_to_earlier_years">"Scroll to show earlier years"</string>
    <string msgid="5727367015496556177" name="m3c_date_picker_scroll_to_later_years">"Scroll to show later years"</string>
    <string msgid="1804346892470238807" name="m3c_date_picker_switch_to_calendar_mode">"Switch to calendar input mode"</string>
    <string msgid="395627960681594326" name="m3c_date_picker_switch_to_day_selection">"Swipe to select a year or tap to switch back to selecting a day"</string>
    <string msgid="2219746470065162704" name="m3c_date_picker_switch_to_input_mode">"Switch to text input mode"</string>
    <string msgid="7142101321095356500" name="m3c_date_picker_switch_to_next_month">"Change to next month"</string>
    <string msgid="228438865139394590" name="m3c_date_picker_switch_to_previous_month">"Change to previous month"</string>
    <string msgid="791651718641787594" name="m3c_date_picker_switch_to_year_selection">"Switch to selecting a year"</string>
    <string msgid="7430790972741451689" name="m3c_date_picker_title">"Select date"</string>
    <string msgid="3199387177749801575" name="m3c_date_picker_today_description">"Today"</string>
    <string msgid="2068382232816991922" name="m3c_date_picker_year_picker_pane_title">"Year picker visible"</string>
    <string msgid="3190049423327661366" name="m3c_date_range_input_invalid_range_input">"Invalid date range input"</string>
    <string msgid="3148384720560189467" name="m3c_date_range_input_title">"Enter dates"</string>
    <string msgid="2138321128465719402" name="m3c_date_range_picker_day_in_range">"In range"</string>
    <string msgid="4947636797751277713" name="m3c_date_range_picker_end_headline">"End date"</string>
    <string msgid="602077859540990149" name="m3c_date_range_picker_scroll_to_next_month">"Scroll to show the next month"</string>
    <string msgid="4592174524846109496" name="m3c_date_range_picker_scroll_to_previous_month">"Scroll to show the previous month"</string>
    <string msgid="4665981448952749820" name="m3c_date_range_picker_start_headline">"Start date"</string>
    <string msgid="3134165431120340385" name="m3c_date_range_picker_title">"Select dates"</string>
    <string msgid="7617233117134790350" name="m3c_dialog">"Dialogue"</string>
    <string msgid="3177828188723359358" name="m3c_dropdown_menu_collapsed">"Collapsed"</string>
    <string msgid="2360841780724299882" name="m3c_dropdown_menu_expanded">"Expanded"</string>
    <string msgid="6152806324422087846" name="m3c_search_bar_search">"Search"</string>
    <string msgid="6152755701819882931" name="m3c_snackbar_dismiss">"Dismiss"</string>
    <string msgid="7655536806087401899" name="m3c_suggestions_available">"Suggestions below"</string>
    <string msgid="2786685010796619560" name="m3c_time_picker_am">"a.m."</string>
    <string msgid="2349193472625211372" name="m3c_time_picker_hour">"Hour"</string>
    <string msgid="9179527532316922345" name="m3c_time_picker_hour_24h_suffix">"%1$d hours"</string>
    <string msgid="8876759303332837035" name="m3c_time_picker_hour_selection">"Select hour"</string>
    <string msgid="3458167507790628988" name="m3c_time_picker_hour_suffix">"%1$d o\'clock"</string>
    <string msgid="6973808109666874069" name="m3c_time_picker_hour_text_field">"for hour"</string>
    <string msgid="4313071914266462005" name="m3c_time_picker_minute">"Minute"</string>
    <string msgid="4699133535056739733" name="m3c_time_picker_minute_selection">"Select minutes"</string>
    <string msgid="5064177921781937179" name="m3c_time_picker_minute_suffix">"%1$d minutes"</string>
    <string msgid="7661234488295443182" name="m3c_time_picker_minute_text_field">"for minutes"</string>
    <string msgid="5865171949528594571" name="m3c_time_picker_period_toggle_description">"Select a.m. or p.m."</string>
    <string msgid="6616362054113087709" name="m3c_time_picker_pm">"p.m."</string>
    <string msgid="1805687647081129904" name="m3c_tooltip_long_press_label">"Show tooltip"</string>
    <string msgid="5460405025248574620" name="m3c_tooltip_pane_description">"Tooltip"</string>
    <string msgid="542007171693138492" name="navigation_menu">"Navigation menu"</string>
    <string msgid="6610465462668679431" name="not_selected">"Not selected"</string>
    <string msgid="5941395253238309765" name="range_end">"Range end"</string>
    <string msgid="7097486360902471446" name="range_start">"Range start"</string>
    <string msgid="6264217191555673260" name="search_menu_title">"Search"</string>
    <string msgid="6043586758067023" name="selected">"Selected"</string>
    <string msgid="4139871816613051306" name="state_empty">"Empty"</string>
    <string msgid="5834637030682409883" name="state_off">"Off"</string>
    <string msgid="1490883874751719580" name="state_on">"On"</string>
    <string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string>
    <string name="sumup_app_certificate_error">Network certificate validation has failed! Please ensure that your device is connected to a secure internet connection.</string>
    <string name="sumup_app_update_required_title">Update Required</string>
    <string name="sumup_autoreceipt_btn_stop">Stop automatic receipts</string>
    <string name="sumup_autoreceipt_customer_credentials_error">Please enter only customer credentials</string>
    <string name="sumup_autoreceipt_keypoint_customers">At checkout, provide only the customer’s email or phone.</string>
    <string name="sumup_autoreceipt_keypoint_email_phone">Don’t provide your own email or phone, or all your customer’s receipts will be sent to you instead of them.</string>
    <string name="sumup_autoreceipt_keypoint_merchants">Customers will receive receipts for purchases made at all SumUp merchants.</string>
    <string name="sumup_autoreceipt_keypoint_share">We’ll never share or sell our customer data.</string>
    <string name="sumup_autoreceipt_label_sent">А receipt was automatically sent to %1$s</string>
    <string name="sumup_autoreceipt_label_subscribe_toggle">Automatically send receipts from SumUp merchants from now on</string>
    <string name="sumup_autoreceipt_off_text">Off</string>
    <string name="sumup_autoreceipt_on_text">On</string>
    <string name="sumup_autoreceipt_privacy_policy_link">Privacy Policy</string>
    <string name="sumup_autoreceipt_privacy_policy_text">Learn more in our %1$s</string>
    <string name="sumup_autoreceipt_rules_link">Set of Rules</string>
    <string name="sumup_autoreceipt_rules_text">Please note that our %1$s apply when offering automated receipts to your customers.</string>
    <string name="sumup_autoreceipt_see_privacy_policy">See our %1$s for more info</string>
    <string name="sumup_autoreceipt_send_text">Send automatic receipts</string>
    <string name="sumup_autoreceipt_stop_alert_body">Be sure to confirm that the customer no longer wants to receive receipts before you do this.</string>
    <string name="sumup_autoreceipt_stop_alert_btn_cancel">Cancel</string>
    <string name="sumup_autoreceipt_stop_alert_btn_destructive">Stop receipts</string>
    <string name="sumup_autoreceipt_stop_alert_title">Stop automatic receipts?</string>
    <string name="sumup_autoreceipt_stop_toast_error_subtitle">Please try again later to turn off automatic receipts.</string>
    <string name="sumup_autoreceipt_stop_toast_error_title">Couldn\'t complete request</string>
    <string name="sumup_autoreceipt_stop_toast_success_subtitle">Automatic receipts stopped</string>
    <string name="sumup_autoreceipt_subtitle_label_sent">The receipt was sent to %1$s</string>
    <string name="sumup_autoreceipt_text">Аutomatic receipts</string>
    <string name="sumup_autoreceipt_title_label_sent">Sent!</string>
    <string name="sumup_bt_connection_failure_description">Restart your %1$s and card reader and try again.</string>
    <string name="sumup_bt_connection_failure_title">Couldn’t find your %1$s</string>
    <string name="sumup_bt_reset_air_message">Press and hold the power button until the display shows %1$s. This can take a moment. Once %2$s pops up, promptly release the power button.</string>
    <string name="sumup_bt_reset_instruction_air_1_description">Make sure it’s disconnected from the power supply and press the power button to turn off.</string>
    <string name="sumup_bt_reset_instruction_air_1_title">Unplug and turn off your Air reader</string>
    <string name="sumup_bt_reset_instruction_air_2_description">Hold the power button down for a few seconds until you see “Release…”.</string>
    <string name="sumup_bt_reset_instruction_air_2_title">Press and hold the power button</string>
    <string name="sumup_bt_reset_instruction_air_3_description">After releasing the power button, you should hear a beep confirming that Bluetooth has been reset.</string>
    <string name="sumup_bt_reset_instruction_air_3_title">Release button and listen for beep</string>
    <string name="sumup_bt_reset_instruction_solo_1_description">Tap the arrow at the top of Solo to go to “Connections”.</string>
    <string name="sumup_bt_reset_instruction_solo_1_title">On Solo, go to the connections menu</string>
    <string name="sumup_bt_reset_instruction_solo_2_description">Go to “Bluetooth” and turn the toggle off and then back on.</string>
    <string name="sumup_bt_reset_instruction_solo_2_title">Turn Solo’s Bluetooth back on</string>
    <string name="sumup_bt_reset_instruction_three_g_1_description">Tap the up or down arrow on the card reader.</string>
    <string name="sumup_bt_reset_instruction_three_g_1_title">Go to “Device settings”</string>
    <string name="sumup_bt_reset_instruction_three_g_2_title">Select “Connection type”</string>
    <string name="sumup_bt_reset_instruction_three_g_3_description">Select “Off” and tap the green tick to confirm.</string>
    <string name="sumup_bt_reset_instruction_three_g_3_title">Turn Bluetooth off</string>
    <string name="sumup_bt_reset_instruction_three_g_4_description">Select “On” and tap the green tick to confirm.</string>
    <string name="sumup_bt_reset_instruction_three_g_4_title">Turn Bluetooth back on</string>
    <string name="sumup_bt_reset_instructions_message">Follow these two steps to solve the connection issue between this device and your card reader.</string>
    <string name="sumup_bt_reset_instructions_title">Resetting Bluetooth</string>
    <string name="sumup_bt_reset_mobile_description">Tap the button below to reset Bluetooth on this device.</string>
    <string name="sumup_bt_reset_on_device_description_done">Done</string>
    <string name="sumup_bt_reset_on_device_description_from_settings">Turn Bluetooth off and back on</string>
    <string name="sumup_bt_reset_on_device_description_not_done">It will only take a second</string>
    <string name="sumup_bt_reset_on_device_title">On this device</string>
    <string name="sumup_bt_reset_on_reader_description">See how to do this</string>
    <string name="sumup_bt_reset_on_reader_title">On your %1$s</string>
    <string name="sumup_bt_reset_option_phone">On your phone</string>
    <string name="sumup_bt_reset_option_reader">On your card reader</string>
    <string name="sumup_bt_reset_option_tablet">On your tablet</string>
    <string name="sumup_bt_setup_instruction_solo_1_description">Press the power button to turn Solo on.</string>
    <string name="sumup_bt_setup_instruction_solo_1_title">Make sure Solo is on</string>
    <string name="sumup_bt_setup_instruction_solo_2_description">Tap the arrow at the top of the Solo screen and go to “Connections”.</string>
    <string name="sumup_bt_setup_instruction_solo_2_title">Go to “Connections” in the menu</string>
    <string name="sumup_bt_setup_instruction_solo_3_description">Go to “Bluetooth” and turn the toggle on.</string>
    <string name="sumup_bt_setup_instruction_solo_3_title">Turn Solo’s Bluetooth on</string>
    <string name="sumup_bt_setup_instruction_three_g_1_description">Tap the upwards or downwards arrow on the card reader to access.</string>
    <string name="sumup_bt_setup_instruction_three_g_1_title">Go to “Device settings”</string>
    <string name="sumup_bt_setup_instruction_three_g_2_title">Select “Connection type”</string>
    <string name="sumup_bt_setup_instruction_three_g_3_description">Select “On” to turn on Bluetooth.</string>
    <string name="sumup_bt_setup_instruction_three_g_3_title">Choose “Bluetooth”</string>
    <string name="sumup_bt_setup_instruction_three_g_4_title">Press the green tick to confirm</string>
    <string name="sumup_bt_troubleshooting_reader_selection_description">Select your card reader</string>
    <string name="sumup_bt_troubleshooting_reader_selection_onboarding_title">Set up your card reader</string>
    <string name="sumup_bt_troubleshooting_reader_selection_title">What are you setting up?</string>
    <string name="sumup_btn_call_support">Call now</string>
    <string name="sumup_btn_cancel">Cancel</string>
    <string name="sumup_btn_connect">Connect</string>
    <string name="sumup_btn_dismiss">Dismiss</string>
    <string name="sumup_btn_done">Done</string>
    <string name="sumup_btn_edit">Edit</string>
    <string name="sumup_btn_go_to_support">Go to support</string>
    <string name="sumup_btn_later">Later</string>
    <string name="sumup_btn_new_set_up">New set-up</string>
    <string name="sumup_btn_next">Next</string>
    <string name="sumup_btn_no">No</string>
    <string name="sumup_btn_not_my_reader">Not my reader</string>
    <string name="sumup_btn_ok">OK</string>
    <string name="sumup_btn_previous">Previous</string>
    <string name="sumup_btn_proceed">Proceed</string>
    <string name="sumup_btn_rescan">Rescan</string>
    <string name="sumup_btn_retry">Retry</string>
    <string name="sumup_btn_scan">Scan</string>
    <string name="sumup_btn_send">Send</string>
    <string name="sumup_btn_send_to_mobile">Send to mobile</string>
    <string name="sumup_btn_skip">Skip</string>
    <string name="sumup_btn_update">Update</string>
    <string name="sumup_btn_yes">Yes</string>
    <string name="sumup_btn_yes_connect">Yes, connect</string>
    <string name="sumup_btn_yes_scan">Yes, scan</string>
    <string name="sumup_button_retry">Retry</string>
    <string name="sumup_cancel_pending_payment_message">Are you sure you want to cancel this checkout?</string>
    <string name="sumup_cancel_pending_payment_title">Payment pending</string>
    <string name="sumup_card_reader_battery_level_label">Battery level</string>
    <string name="sumup_card_reader_battery_level_percentage">%1$d%%</string>
    <string name="sumup_card_reader_battery_text">Battery</string>
    <string name="sumup_card_reader_bluetooth_software_version_text">Bluetooth software</string>
    <string name="sumup_card_reader_connect_button_text">Connect</string>
    <string name="sumup_card_reader_error_dialog_header">Couldn\'t connect</string>
    <string name="sumup_card_reader_error_dialog_positive">Try again</string>
    <string name="sumup_card_reader_error_dialog_title">Couldn\'t connect to the card reader. Please try again.</string>
    <string name="sumup_card_reader_last_digits_text">Last digits</string>
    <string name="sumup_card_reader_low_battery">Low battery, please charge device</string>
    <string name="sumup_card_reader_not_saved_body">Connect card reader</string>
    <string name="sumup_card_reader_not_saved_title">Get started with card payments</string>
    <string name="sumup_card_reader_not_saved_title_br">Get started with Top / Solo</string>
    <string name="sumup_card_reader_serial_number_text">Serial number</string>
    <string name="sumup_card_reader_setting_enable_generic">Accept payments with card reader</string>
    <string name="sumup_card_reader_setting_enable_generic_br">Accept payments with Top / Solo</string>
    <string name="sumup_card_reader_setting_enable_subtitle">Show as a payment method during checkout.</string>
    <string name="sumup_card_reader_setting_switch_card_readers">Switch to another card reader</string>
    <string name="sumup_card_reader_software_version_text">Software</string>
    <string name="sumup_checking_firmware_info">Checking for firmware updates</string>
    <string name="sumup_checkout_power_on_reader">Power on card terminal</string>
    <string name="sumup_confirm_pairing_air">Please confirm the pairing request on your %1$s AND on the card reader.</string>
    <string name="sumup_confirm_pairing_air_ios">If prompted please confirm the pairing request on your %1$s AND on the card reader.</string>
    <string name="sumup_connection_lost">Connection lost</string>
    <string name="sumup_contacts_permission_request_title">Contacts</string>
    <string name="sumup_could_not_find_your_reader">Couldn’t find your reader</string>
    <string name="sumup_error">Error</string>
    <string name="sumup_error_cash_payment_cannot_save_message">Unable to save changes. Please try again or contact support.</string>
    <string name="sumup_error_cash_payment_cannot_save_title">Cash payment</string>
    <string name="sumup_error_payment_method_required">Payment method required</string>
    <string name="sumup_external_storage_permission_request_text">In order to download payout reports, SumUp needs access to the external storage of your device</string>
    <string name="sumup_failed_transaction">Transaction failed</string>
    <string name="sumup_firmware_update_canceled">Update cancelled</string>
    <string name="sumup_grant_usb_permission">To proceed, you must grant permission to access via USB.</string>
    <string name="sumup_help">Help</string>
    <string name="sumup_home_card_acceptance_cta_button_title">Do it now</string>
    <string name="sumup_home_card_acceptance_subtitle">Set up your card reader and start taking payments.</string>
    <string name="sumup_home_card_acceptance_title">Ready, set, get paid!</string>
    <string name="sumup_identity_login_error_generic_message">Something went wrong!\nPlease try logging in again.</string>
    <string name="sumup_identity_login_error_session_expired_message">Your session has expired. Please log in again.</string>
    <string name="sumup_identity_migration_to_auth_login_message_content">Please log in again.</string>
    <string name="sumup_identity_migration_to_auth_login_message_title">We\'ve updated the app</string>
    <string name="sumup_installment_cancelled">Cancelled</string>
    <string name="sumup_items_many">%1$s items</string>
    <string name="sumup_items_none">No items</string>
    <string name="sumup_items_one">One item</string>
    <string name="sumup_l10n_solo_printer_communication_error_description">The connection failed, please retry.</string>
    <string name="sumup_l10n_solo_printer_escpos_receipt_download_failed_description">Failed to download receipt for Solo printer.</string>
    <string name="sumup_l10n_solo_printer_not_attached_description">Make sure Solo is in the printer and the printer battery is charged.</string>
    <string name="sumup_l10n_solo_printer_not_found_title">Printer not found.</string>
    <string name="sumup_l10n_solo_printer_offline_description">Couldn\'t connect to Solo.</string>
    <string name="sumup_l10n_solo_printer_outdated_firmware_action">Update</string>
    <string name="sumup_l10n_solo_printer_outdated_firmware_description">Update software to print.</string>
    <string name="sumup_l10n_solo_printer_outdated_firmware_title">Solo needs an update.</string>
    <string name="sumup_l10n_solo_printer_print_failed_generic_description">Check the printer lid and paper roll and try again.</string>
    <string name="sumup_l10n_solo_printer_print_failed_title">Printing failed.</string>
    <string name="sumup_label_external_storage">External Storage</string>
    <string name="sumup_learn_how">Learn how.</string>
    <string name="sumup_location_permission_request_text">In order to provide a secure service, SumUp needs to know where transactions take place. Therefore, SumUp needs to access your location while you use the app.</string>
    <string name="sumup_location_permission_request_title">Location services</string>
    <string name="sumup_login_btn_forgot_password">Forgot password</string>
    <string name="sumup_login_btn_title">Log in</string>
    <string name="sumup_login_failed_message">A problem occurred configuring your profile. Please try again later or contact our Support Team.</string>
    <string name="sumup_login_failed_title">Login failed</string>
    <string name="sumup_merchant_activation_code">Activation code</string>
    <string name="sumup_merchant_activation_code_title">Activate Total / Super / On</string>
    <string name="sumup_navigation_payment_methods">Payment methods</string>
    <string name="sumup_navigation_payment_methods_multiple_options">Payment methods</string>
    <string name="sumup_nearby_devices_permission_request_text">To continue, SumUp needs your permission to access devices in the immediate area.</string>
    <string name="sumup_nearby_devices_permission_request_title">Nearby devices</string>
    <string name="sumup_no_account_question">Don\'t have a profile yet?</string>
    <string name="sumup_no_air_usb_device">This device is unknown. Please connect a SumUp Air Card Reader.</string>
    <string name="sumup_no_bluetooth_on_solo">No Bluetooth on Solo?</string>
    <string name="sumup_no_usb_device">Reconnect your card reader via a USB cable.</string>
    <string name="sumup_order_charge">Charge</string>
    <string name="sumup_pax_setup_add_terminal">New terminal</string>
    <string name="sumup_payment_setting_card_reader">Card Reader</string>
    <string name="sumup_payment_to_title">Paid to</string>
    <string name="sumup_permission_explanation_continue">Continue</string>
    <string name="sumup_pos_lite">POS Lite</string>
    <string name="sumup_pp_setup_air_no_btle">Your device does not support Bluetooth 4.0. Please check whether a software update is available for your device.</string>
    <string name="sumup_pp_setup_charge_batteries">Please make sure the batteries in the card reader are charged.</string>
    <string name="sumup_pp_setup_connected">Connected!</string>
    <string name="sumup_pp_setup_connection_failed">Could not connect. Please try again or call SumUp Support at %1$s</string>
    <string name="sumup_pp_setup_do_firmware_update_now_mandatory">Please make sure to update before %1$s.</string>
    <string name="sumup_pp_setup_firmware_cancel_sure">Are you sure you want to cancel the update?</string>
    <string name="sumup_pp_setup_firmware_update_do_now">A firmware update is available for your card reader. The update will take a few minutes.\n\nWe strongly recommend that you install the update to ensure optimal performance of your device.</string>
    <string name="sumup_pp_setup_firmware_update_installing">Installing, please wait..</string>
    <string name="sumup_pp_setup_firmware_update_minutes_remaining">%1$s minutes remaining</string>
    <string name="sumup_pp_setup_firmware_update_one_minute_remaining">One minute remaining</string>
    <string name="sumup_pp_setup_multiple_devices">Multiple terminals discovered</string>
    <string name="sumup_pp_setup_searching">Scanning for card readers</string>
    <string name="sumup_pp_setup_select_bt_device">Compare the last digits on your screen to the serial number on the back of the terminal.</string>
    <string name="sumup_pp_setup_text_firmware_update_ongoing">Please do not use this device until the update is completed.</string>
    <string name="sumup_print_autoprint_receipts">Always print receipts</string>
    <string name="sumup_print_no_printers_found">No printers found</string>
    <string name="sumup_print_open_cash_drawer">Open cash drawer</string>
    <string name="sumup_print_open_drawer_automatically">Open drawer automatically</string>
    <string name="sumup_print_searching_printers">Searching for printers…</string>
    <string name="sumup_print_select_printer">Select printer</string>
    <string name="sumup_print_test_page">Print test page</string>
    <string name="sumup_printer_settings_copies_label">Number of copies</string>
    <string name="sumup_printer_settings_disabled">Disabled</string>
    <string name="sumup_printer_settings_none_paired">Not paired</string>
    <string name="sumup_printer_settings_toggle_label">Enable receipt printing</string>
    <string name="sumup_reader_attribute_unknown">Unknown</string>
    <string name="sumup_reader_connecting_text">Connecting to card reader</string>
    <string name="sumup_reader_connecting_title">Connecting</string>
    <string name="sumup_reader_home_item_status_update">Update device</string>
    <string name="sumup_reader_not_found_message">Your card reader ending in %1$s wasn’t found. Please make sure that the reader is switched on and within range.</string>
    <string name="sumup_reader_off_and_unplugged_message">Make sure the device is switched off and disconnected from the power supply.</string>
    <string name="sumup_reader_off_and_unplugged_title">Unplug and switch off</string>
    <string name="sumup_reader_payment">Payment</string>
    <string name="sumup_reader_payment_cancelled">Payment was cancelled</string>
    <string name="sumup_receipt_email_text">Email</string>
    <string name="sumup_receipt_field_customer_email">Customer email</string>
    <string name="sumup_receipt_field_customer_phone">Customer phone</string>
    <string name="sumup_receipt_field_email_address">Email address</string>
    <string name="sumup_receipt_field_mobile_number">Mobile number</string>
    <string name="sumup_receipt_sms_text">SMS</string>
    <string name="sumup_receipt_title_send_receipt">Send receipt</string>
    <string name="sumup_receipt_title_send_to_mobile">Send to mobile</string>
    <string name="sumup_reset_bluetooth">Reset Bluetooth</string>
    <string name="sumup_reset_bluetooth_on_reader">Reset Bluetooth on %1$s</string>
    <string name="sumup_reset_reader_confirmation_message">After releasing the power button, you will hear a beep confirming that Bluetooth has been reset.</string>
    <string name="sumup_reset_reader_confirmation_title">Reset complete</string>
    <string name="sumup_reset_reader_title">Start the reset</string>
    <string name="sumup_saved_reader_home_item_status_is_inactive">Inactive</string>
    <string name="sumup_saved_reader_home_item_status_is_ready">Ready</string>
    <string name="sumup_send_receipt_email_field_label">Email address</string>
    <string name="sumup_send_receipt_phone_number_label">Mobile phone number</string>
    <string name="sumup_service_unavailable_message">We\'re working hard to fix the issue. Please try again later.</string>
    <string name="sumup_service_unavailable_title">Service unavailable</string>
    <string name="sumup_sign_up">Sign up</string>
    <string name="sumup_signature_sign_here">Please sign here</string>
    <string name="sumup_signature_try_again_button_title">Retry</string>
    <string name="sumup_solo_app_update_message">Please update your SumUp App to continue using Solo</string>
    <string name="sumup_solo_plug_and_play_connection_disconnected_message">Solo disconnected</string>
    <string name="sumup_solo_plug_and_play_connection_failure_message">Solo couldn\'t connect</string>
    <string name="sumup_solo_plug_and_play_connection_success_message">Solo connected and ready to transact</string>
    <string name="sumup_successful_transaction">Transaction successful</string>
    <string name="sumup_tipping">Tipping</string>
    <string name="sumup_tipping_signature_including_tip_hint">(incl. tip %1$s)</string>
    <string name="sumup_transaction_not_processed_desc">The transaction could not be processed due to network issues.</string>
    <string name="sumup_transaction_not_processed_details">The card has not been charged. You can safely try the transaction again.</string>
    <string name="sumup_troubleshooting_how_to_turn_on_bluetooth">How to turn on Bluetooth</string>
    <string name="sumup_troubleshooting_reader_not_found_default_case_description">Then turn it on and keep it nearby to connect.</string>
    <string name="sumup_troubleshooting_reader_not_found_default_case_title">Make sure your card reader is charged up</string>
    <string name="sumup_troubleshooting_turn_on_bluetooth_on_reader_description_generic">Make sure your %1$s%2$s is powered on, with Bluetooth on, and it’s close by.</string>
    <string name="sumup_troubleshooting_turn_on_bluetooth_on_reader_description_solo">Make sure the Bluetooth icon is in the top right corner of your Solo.</string>
    <string name="sumup_troubleshooting_turn_on_bluetooth_on_reader_title">Is Bluetooth in %1$s on?</string>
    <string name="sumup_troubleshooting_turn_on_reader_description">Make sure your %1$s%2$s is powered on and close by.</string>
    <string name="sumup_troubleshooting_turn_on_reader_title">Turn on your %1$s</string>
    <string name="sumup_turn_bluetooth_off_on">Turn Bluetooth off &amp; on</string>
    <string name="sumup_turn_bluetooth_off_on_on_reader">Turn Bluetooth off &amp; on %1$s</string>
    <string name="sumup_tx_cancel_on_solo">To cancel the payment, please press the cancel button on the card reader.</string>
    <string name="sumup_update_software">Update software.</string>
    <string name="sumup_update_solo_message">1. Open the Solo menu by tapping the top of the reader display\n\n2.Tap \'Connections\' and connect to WiFi\n\n3. Log in if needed\n\n4. Tap \'Settings\', \'Software update\' and then \'Download &amp; install\'\n\n5. Once the update is done, tap \'Connections\' and turn Bluetooth® back on</string>
    <string name="sumup_update_solo_title">Update your Solo card reader</string>
    <string name="sumup_verification_camera_permission_request_title">Camera</string>
    <string name="sumup_verify_phone_number">Verify mobile number</string>
    <string name="sumup_waiting_reader_action">Waiting for customer</string>
    <string msgid="2561197295334830845" name="switch_role">"Switch"</string>
    <string msgid="1672349317127674378" name="tab">"Tab"</string>
    <string msgid="5946805113151406391" name="template_percent">"<ns1:g id="PERCENTAGE">%1$d</ns1:g> per cent."</string>
    <string msgid="3765533235322692011" name="tooltip_description">"tooltip"</string>
    <string msgid="3124740595719787496" name="tooltip_label">"show tooltip"</string>
</resources>