package com.thedasagroup.suminative.work;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class OrderSyncViewModel_Factory implements Factory<OrderSyncViewModel> {
  private final Provider<OrderSyncManager> orderSyncManagerProvider;

  public OrderSyncViewModel_Factory(Provider<OrderSyncManager> orderSyncManagerProvider) {
    this.orderSyncManagerProvider = orderSyncManagerProvider;
  }

  @Override
  public OrderSyncViewModel get() {
    return newInstance(orderSyncManagerProvider.get());
  }

  public static OrderSyncViewModel_Factory create(
      Provider<OrderSyncManager> orderSyncManagerProvider) {
    return new OrderSyncViewModel_Factory(orderSyncManagerProvider);
  }

  public static OrderSyncViewModel newInstance(OrderSyncManager orderSyncManager) {
    return new OrderSyncViewModel(orderSyncManager);
  }
}
