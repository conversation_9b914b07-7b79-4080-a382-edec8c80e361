package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.WaitersRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesWaitersRepositoryFactory implements Factory<WaitersRepository> {
  @Override
  public WaitersRepository get() {
    return providesWaitersRepository();
  }

  public static RepoModule_ProvidesWaitersRepositoryFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static WaitersRepository providesWaitersRepository() {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesWaitersRepository());
  }

  private static final class InstanceHolder {
    static final RepoModule_ProvidesWaitersRepositoryFactory INSTANCE = new RepoModule_ProvidesWaitersRepositoryFactory();
  }
}
