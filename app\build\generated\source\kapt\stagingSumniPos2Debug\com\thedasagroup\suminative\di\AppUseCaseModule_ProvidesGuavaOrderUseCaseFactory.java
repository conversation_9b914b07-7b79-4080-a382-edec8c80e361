package com.thedasagroup.suminative.di;

import com.instacart.truetime.time.TrueTimeImpl;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.MyGuavaRepository;
import com.thedasagroup.suminative.data.repo.StockRepository;
import com.thedasagroup.suminative.ui.products.PlaceOnlineOrderUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesGuavaOrderUseCaseFactory implements Factory<PlaceOnlineOrderUseCase> {
  private final Provider<StockRepository> stockRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  private final Provider<TrueTimeImpl> trueTimeImplProvider;

  private final Provider<MyGuavaRepository> myGuavaRepositoryProvider;

  public AppUseCaseModule_ProvidesGuavaOrderUseCaseFactory(
      Provider<StockRepository> stockRepositoryProvider, Provider<Prefs> prefsProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider,
      Provider<MyGuavaRepository> myGuavaRepositoryProvider) {
    this.stockRepositoryProvider = stockRepositoryProvider;
    this.prefsProvider = prefsProvider;
    this.trueTimeImplProvider = trueTimeImplProvider;
    this.myGuavaRepositoryProvider = myGuavaRepositoryProvider;
  }

  @Override
  public PlaceOnlineOrderUseCase get() {
    return providesGuavaOrderUseCase(stockRepositoryProvider.get(), prefsProvider.get(), trueTimeImplProvider.get(), myGuavaRepositoryProvider.get());
  }

  public static AppUseCaseModule_ProvidesGuavaOrderUseCaseFactory create(
      Provider<StockRepository> stockRepositoryProvider, Provider<Prefs> prefsProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider,
      Provider<MyGuavaRepository> myGuavaRepositoryProvider) {
    return new AppUseCaseModule_ProvidesGuavaOrderUseCaseFactory(stockRepositoryProvider, prefsProvider, trueTimeImplProvider, myGuavaRepositoryProvider);
  }

  public static PlaceOnlineOrderUseCase providesGuavaOrderUseCase(StockRepository stockRepository,
      Prefs prefs, TrueTimeImpl trueTimeImpl, MyGuavaRepository myGuavaRepository) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesGuavaOrderUseCase(stockRepository, prefs, trueTimeImpl, myGuavaRepository));
  }
}
