package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.RewardsRepository;
import com.thedasagroup.suminative.domain.rewards.GetAllCustomersUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesGetAllCustomersUseCaseFactory implements Factory<GetAllCustomersUseCase> {
  private final Provider<RewardsRepository> rewardsRepositoryProvider;

  public AppUseCaseModule_ProvidesGetAllCustomersUseCaseFactory(
      Provider<RewardsRepository> rewardsRepositoryProvider) {
    this.rewardsRepositoryProvider = rewardsRepositoryProvider;
  }

  @Override
  public GetAllCustomersUseCase get() {
    return providesGetAllCustomersUseCase(rewardsRepositoryProvider.get());
  }

  public static AppUseCaseModule_ProvidesGetAllCustomersUseCaseFactory create(
      Provider<RewardsRepository> rewardsRepositoryProvider) {
    return new AppUseCaseModule_ProvidesGetAllCustomersUseCaseFactory(rewardsRepositoryProvider);
  }

  public static GetAllCustomersUseCase providesGetAllCustomersUseCase(
      RewardsRepository rewardsRepository) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesGetAllCustomersUseCase(rewardsRepository));
  }
}
