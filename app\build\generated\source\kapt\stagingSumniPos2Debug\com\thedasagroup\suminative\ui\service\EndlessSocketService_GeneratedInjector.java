package com.thedasagroup.suminative.ui.service;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ServiceComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = EndlessSocketService.class
)
@GeneratedEntryPoint
@InstallIn(ServiceComponent.class)
public interface EndlessSocketService_GeneratedInjector {
  void injectEndlessSocketService(EndlessSocketService endlessSocketService);
}
