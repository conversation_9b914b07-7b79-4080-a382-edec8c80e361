package com.thedasagroup.suminative.work;

import android.content.Context;
import androidx.work.WorkerParameters;
import com.thedasagroup.suminative.data.repo.LogsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class UploadLogsWorker_Factory {
  private final Provider<LogsRepository> logsRepositoryProvider;

  public UploadLogsWorker_Factory(Provider<LogsRepository> logsRepositoryProvider) {
    this.logsRepositoryProvider = logsRepositoryProvider;
  }

  public UploadLogsWorker get(Context appContext, WorkerParameters params) {
    return newInstance(logsRepositoryProvider.get(), appContext, params);
  }

  public static UploadLogsWorker_Factory create(Provider<LogsRepository> logsRepositoryProvider) {
    return new UploadLogsWorker_Factory(logsRepositoryProvider);
  }

  public static UploadLogsWorker newInstance(LogsRepository logsRepository, Context appContext,
      WorkerParameters params) {
    return new UploadLogsWorker(logsRepository, appContext, params);
  }
}
