package com.thedasagroup.suminative.ui.local_orders;

import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class LocalOrdersViewModel_Factory_Impl implements LocalOrdersViewModel.Factory {
  private final LocalOrdersViewModel_Factory delegateFactory;

  LocalOrdersViewModel_Factory_Impl(LocalOrdersViewModel_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public LocalOrdersViewModel create(LocalOrdersState state) {
    return delegateFactory.get(state);
  }

  public static Provider<LocalOrdersViewModel.Factory> create(
      LocalOrdersViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new LocalOrdersViewModel_Factory_Impl(delegateFactory));
  }

  public static dagger.internal.Provider<LocalOrdersViewModel.Factory> createFactoryProvider(
      LocalOrdersViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new LocalOrdersViewModel_Factory_Impl(delegateFactory));
  }
}
