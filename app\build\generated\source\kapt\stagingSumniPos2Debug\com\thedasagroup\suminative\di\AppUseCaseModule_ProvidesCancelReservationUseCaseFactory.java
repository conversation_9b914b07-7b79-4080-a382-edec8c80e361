package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.ReservationsRepository;
import com.thedasagroup.suminative.ui.reservations.CancelReservationUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesCancelReservationUseCaseFactory implements Factory<CancelReservationUseCase> {
  private final Provider<ReservationsRepository> reservationsRepositoryProvider;

  public AppUseCaseModule_ProvidesCancelReservationUseCaseFactory(
      Provider<ReservationsRepository> reservationsRepositoryProvider) {
    this.reservationsRepositoryProvider = reservationsRepositoryProvider;
  }

  @Override
  public CancelReservationUseCase get() {
    return providesCancelReservationUseCase(reservationsRepositoryProvider.get());
  }

  public static AppUseCaseModule_ProvidesCancelReservationUseCaseFactory create(
      Provider<ReservationsRepository> reservationsRepositoryProvider) {
    return new AppUseCaseModule_ProvidesCancelReservationUseCaseFactory(reservationsRepositoryProvider);
  }

  public static CancelReservationUseCase providesCancelReservationUseCase(
      ReservationsRepository reservationsRepository) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesCancelReservationUseCase(reservationsRepository));
  }
}
