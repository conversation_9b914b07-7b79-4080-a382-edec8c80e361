package com.thedasagroup.suminative.di;

import com.instacart.truetime.time.TrueTimeImpl;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.ReservationsRepository;
import com.thedasagroup.suminative.ui.reservations.GetAllReservationsUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesGetAllReservationsUseCaseFactory implements Factory<GetAllReservationsUseCase> {
  private final Provider<ReservationsRepository> reservationsRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  private final Provider<TrueTimeImpl> trueTimeImplProvider;

  public AppUseCaseModule_ProvidesGetAllReservationsUseCaseFactory(
      Provider<ReservationsRepository> reservationsRepositoryProvider,
      Provider<Prefs> prefsProvider, Provider<TrueTimeImpl> trueTimeImplProvider) {
    this.reservationsRepositoryProvider = reservationsRepositoryProvider;
    this.prefsProvider = prefsProvider;
    this.trueTimeImplProvider = trueTimeImplProvider;
  }

  @Override
  public GetAllReservationsUseCase get() {
    return providesGetAllReservationsUseCase(reservationsRepositoryProvider.get(), prefsProvider.get(), trueTimeImplProvider.get());
  }

  public static AppUseCaseModule_ProvidesGetAllReservationsUseCaseFactory create(
      Provider<ReservationsRepository> reservationsRepositoryProvider,
      Provider<Prefs> prefsProvider, Provider<TrueTimeImpl> trueTimeImplProvider) {
    return new AppUseCaseModule_ProvidesGetAllReservationsUseCaseFactory(reservationsRepositoryProvider, prefsProvider, trueTimeImplProvider);
  }

  public static GetAllReservationsUseCase providesGetAllReservationsUseCase(
      ReservationsRepository reservationsRepository, Prefs prefs, TrueTimeImpl trueTimeImpl) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesGetAllReservationsUseCase(reservationsRepository, prefs, trueTimeImpl));
  }
}
