package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.database.CategoryRepository;
import com.thedasagroup.suminative.data.database.DatabaseManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CategoryModule_ProvideCategoryRepositoryFactory implements Factory<CategoryRepository> {
  private final Provider<DatabaseManager> databaseManagerProvider;

  public CategoryModule_ProvideCategoryRepositoryFactory(
      Provider<DatabaseManager> databaseManagerProvider) {
    this.databaseManagerProvider = databaseManagerProvider;
  }

  @Override
  public CategoryRepository get() {
    return provideCategoryRepository(databaseManagerProvider.get());
  }

  public static CategoryModule_ProvideCategoryRepositoryFactory create(
      Provider<DatabaseManager> databaseManagerProvider) {
    return new CategoryModule_ProvideCategoryRepositoryFactory(databaseManagerProvider);
  }

  public static CategoryRepository provideCategoryRepository(DatabaseManager databaseManager) {
    return Preconditions.checkNotNullFromProvides(CategoryModule.INSTANCE.provideCategoryRepository(databaseManager));
  }
}
