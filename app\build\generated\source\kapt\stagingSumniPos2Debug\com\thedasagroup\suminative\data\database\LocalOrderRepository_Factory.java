package com.thedasagroup.suminative.data.database;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class LocalOrderRepository_Factory implements Factory<LocalOrderRepository> {
  private final Provider<DatabaseManager> databaseManagerProvider;

  public LocalOrderRepository_Factory(Provider<DatabaseManager> databaseManagerProvider) {
    this.databaseManagerProvider = databaseManagerProvider;
  }

  @Override
  public LocalOrderRepository get() {
    return newInstance(databaseManagerProvider.get());
  }

  public static LocalOrderRepository_Factory create(
      Provider<DatabaseManager> databaseManagerProvider) {
    return new LocalOrderRepository_Factory(databaseManagerProvider);
  }

  public static LocalOrderRepository newInstance(DatabaseManager databaseManager) {
    return new LocalOrderRepository(databaseManager);
  }
}
