package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.ReservationsRepository;
import com.thedasagroup.suminative.ui.reservations.GetReservationAreasUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesGetReservationAreasUseCaseFactory implements Factory<GetReservationAreasUseCase> {
  private final Provider<ReservationsRepository> reservationsRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  public AppUseCaseModule_ProvidesGetReservationAreasUseCaseFactory(
      Provider<ReservationsRepository> reservationsRepositoryProvider,
      Provider<Prefs> prefsProvider) {
    this.reservationsRepositoryProvider = reservationsRepositoryProvider;
    this.prefsProvider = prefsProvider;
  }

  @Override
  public GetReservationAreasUseCase get() {
    return providesGetReservationAreasUseCase(reservationsRepositoryProvider.get(), prefsProvider.get());
  }

  public static AppUseCaseModule_ProvidesGetReservationAreasUseCaseFactory create(
      Provider<ReservationsRepository> reservationsRepositoryProvider,
      Provider<Prefs> prefsProvider) {
    return new AppUseCaseModule_ProvidesGetReservationAreasUseCaseFactory(reservationsRepositoryProvider, prefsProvider);
  }

  public static GetReservationAreasUseCase providesGetReservationAreasUseCase(
      ReservationsRepository reservationsRepository, Prefs prefs) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesGetReservationAreasUseCase(reservationsRepository, prefs));
  }
}
