package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.LoginRepository;
import com.thedasagroup.suminative.domain.GetPOSSettingsUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesGetPosSettingsUsecaseFactory implements Factory<GetPOSSettingsUseCase> {
  private final Provider<LoginRepository> loginRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  public AppUseCaseModule_ProvidesGetPosSettingsUsecaseFactory(
      Provider<LoginRepository> loginRepositoryProvider, Provider<Prefs> prefsProvider) {
    this.loginRepositoryProvider = loginRepositoryProvider;
    this.prefsProvider = prefsProvider;
  }

  @Override
  public GetPOSSettingsUseCase get() {
    return providesGetPosSettingsUsecase(loginRepositoryProvider.get(), prefsProvider.get());
  }

  public static AppUseCaseModule_ProvidesGetPosSettingsUsecaseFactory create(
      Provider<LoginRepository> loginRepositoryProvider, Provider<Prefs> prefsProvider) {
    return new AppUseCaseModule_ProvidesGetPosSettingsUsecaseFactory(loginRepositoryProvider, prefsProvider);
  }

  public static GetPOSSettingsUseCase providesGetPosSettingsUsecase(LoginRepository loginRepository,
      Prefs prefs) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesGetPosSettingsUsecase(loginRepository, prefs));
  }
}
