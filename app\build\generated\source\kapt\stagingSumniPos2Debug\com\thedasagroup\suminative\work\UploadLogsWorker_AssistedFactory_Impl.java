package com.thedasagroup.suminative.work;

import android.content.Context;
import androidx.work.WorkerParameters;
import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class UploadLogsWorker_AssistedFactory_Impl implements UploadLogsWorker_AssistedFactory {
  private final UploadLogsWorker_Factory delegateFactory;

  UploadLogsWorker_AssistedFactory_Impl(UploadLogsWorker_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public UploadLogsWorker create(Context context, WorkerParameters parameters) {
    return delegateFactory.get(context, parameters);
  }

  public static Provider<UploadLogsWorker_AssistedFactory> create(
      UploadLogsWorker_Factory delegateFactory) {
    return InstanceFactory.create(new UploadLogsWorker_AssistedFactory_Impl(delegateFactory));
  }

  public static dagger.internal.Provider<UploadLogsWorker_AssistedFactory> createFactoryProvider(
      UploadLogsWorker_Factory delegateFactory) {
    return InstanceFactory.create(new UploadLogsWorker_AssistedFactory_Impl(delegateFactory));
  }
}
