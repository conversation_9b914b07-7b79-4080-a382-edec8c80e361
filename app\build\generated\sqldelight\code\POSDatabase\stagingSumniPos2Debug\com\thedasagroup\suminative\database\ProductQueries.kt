package com.thedasagroup.suminative.database

import app.cash.sqldelight.Query
import app.cash.sqldelight.TransacterImpl
import app.cash.sqldelight.db.QueryResult
import app.cash.sqldelight.db.SqlCursor
import app.cash.sqldelight.db.SqlDriver
import kotlin.Any
import kotlin.Double
import kotlin.Long
import kotlin.String
import kotlin.collections.Collection

public class ProductQueries(
  driver: SqlDriver,
) : TransacterImpl(driver) {
  public fun <T : Any> getAllProducts(mapper: (
    id: Long,
    productId: Long?,
    name: String,
    description: String?,
    additionalInfo: String?,
    category: String?,
    categoryId: Long?,
    price: Double,
    billAmount: Double?,
    tax: Double?,
    vat: Long,
    pic: String?,
    stock: Long,
    ingredients: String?,
    preparationTime: String?,
    servingSize: String?,
    dailyCapacity: Long?,
    discountType: Long?,
    discountedAmount: Double?,
    brandId: Long?,
    businessId: Long?,
    storeId: Long,
    unitId: Long?,
    createdBy: Long?,
    createdOn: String?,
    modifiedBy: Long?,
    modifiedOn: String?,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = Query(759_059_144, arrayOf("ProductEntity"), driver, "Product.sq",
      "getAllProducts",
      "SELECT ProductEntity.id, ProductEntity.productId, ProductEntity.name, ProductEntity.description, ProductEntity.additionalInfo, ProductEntity.category, ProductEntity.categoryId, ProductEntity.price, ProductEntity.billAmount, ProductEntity.tax, ProductEntity.vat, ProductEntity.pic, ProductEntity.stock, ProductEntity.ingredients, ProductEntity.preparationTime, ProductEntity.servingSize, ProductEntity.dailyCapacity, ProductEntity.discountType, ProductEntity.discountedAmount, ProductEntity.brandId, ProductEntity.businessId, ProductEntity.storeId, ProductEntity.unitId, ProductEntity.createdBy, ProductEntity.createdOn, ProductEntity.modifiedBy, ProductEntity.modifiedOn, ProductEntity.createdAt, ProductEntity.updatedAt, ProductEntity.synced FROM ProductEntity ORDER BY name ASC") {
      cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getString(2)!!,
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5),
      cursor.getLong(6),
      cursor.getDouble(7)!!,
      cursor.getDouble(8),
      cursor.getDouble(9),
      cursor.getLong(10)!!,
      cursor.getString(11),
      cursor.getLong(12)!!,
      cursor.getString(13),
      cursor.getString(14),
      cursor.getString(15),
      cursor.getLong(16),
      cursor.getLong(17),
      cursor.getDouble(18),
      cursor.getLong(19),
      cursor.getLong(20),
      cursor.getLong(21)!!,
      cursor.getLong(22),
      cursor.getLong(23),
      cursor.getString(24),
      cursor.getLong(25),
      cursor.getString(26),
      cursor.getLong(27)!!,
      cursor.getLong(28)!!,
      cursor.getLong(29)!!
    )
  }

  public fun getAllProducts(): Query<ProductEntity> = getAllProducts { id, productId, name,
      description, additionalInfo, category, categoryId, price, billAmount, tax, vat, pic, stock,
      ingredients, preparationTime, servingSize, dailyCapacity, discountType, discountedAmount,
      brandId, businessId, storeId, unitId, createdBy, createdOn, modifiedBy, modifiedOn, createdAt,
      updatedAt, synced ->
    ProductEntity(
      id,
      productId,
      name,
      description,
      additionalInfo,
      category,
      categoryId,
      price,
      billAmount,
      tax,
      vat,
      pic,
      stock,
      ingredients,
      preparationTime,
      servingSize,
      dailyCapacity,
      discountType,
      discountedAmount,
      brandId,
      businessId,
      storeId,
      unitId,
      createdBy,
      createdOn,
      modifiedBy,
      modifiedOn,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> getProductById(id: Long, mapper: (
    id: Long,
    productId: Long?,
    name: String,
    description: String?,
    additionalInfo: String?,
    category: String?,
    categoryId: Long?,
    price: Double,
    billAmount: Double?,
    tax: Double?,
    vat: Long,
    pic: String?,
    stock: Long,
    ingredients: String?,
    preparationTime: String?,
    servingSize: String?,
    dailyCapacity: Long?,
    discountType: Long?,
    discountedAmount: Double?,
    brandId: Long?,
    businessId: Long?,
    storeId: Long,
    unitId: Long?,
    createdBy: Long?,
    createdOn: String?,
    modifiedBy: Long?,
    modifiedOn: String?,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = GetProductByIdQuery(id) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getString(2)!!,
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5),
      cursor.getLong(6),
      cursor.getDouble(7)!!,
      cursor.getDouble(8),
      cursor.getDouble(9),
      cursor.getLong(10)!!,
      cursor.getString(11),
      cursor.getLong(12)!!,
      cursor.getString(13),
      cursor.getString(14),
      cursor.getString(15),
      cursor.getLong(16),
      cursor.getLong(17),
      cursor.getDouble(18),
      cursor.getLong(19),
      cursor.getLong(20),
      cursor.getLong(21)!!,
      cursor.getLong(22),
      cursor.getLong(23),
      cursor.getString(24),
      cursor.getLong(25),
      cursor.getString(26),
      cursor.getLong(27)!!,
      cursor.getLong(28)!!,
      cursor.getLong(29)!!
    )
  }

  public fun getProductById(id: Long): Query<ProductEntity> = getProductById(id) { id_, productId,
      name, description, additionalInfo, category, categoryId, price, billAmount, tax, vat, pic,
      stock, ingredients, preparationTime, servingSize, dailyCapacity, discountType,
      discountedAmount, brandId, businessId, storeId, unitId, createdBy, createdOn, modifiedBy,
      modifiedOn, createdAt, updatedAt, synced ->
    ProductEntity(
      id_,
      productId,
      name,
      description,
      additionalInfo,
      category,
      categoryId,
      price,
      billAmount,
      tax,
      vat,
      pic,
      stock,
      ingredients,
      preparationTime,
      servingSize,
      dailyCapacity,
      discountType,
      discountedAmount,
      brandId,
      businessId,
      storeId,
      unitId,
      createdBy,
      createdOn,
      modifiedBy,
      modifiedOn,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> getProductByProductId(productId: Long?, mapper: (
    id: Long,
    productId: Long?,
    name: String,
    description: String?,
    additionalInfo: String?,
    category: String?,
    categoryId: Long?,
    price: Double,
    billAmount: Double?,
    tax: Double?,
    vat: Long,
    pic: String?,
    stock: Long,
    ingredients: String?,
    preparationTime: String?,
    servingSize: String?,
    dailyCapacity: Long?,
    discountType: Long?,
    discountedAmount: Double?,
    brandId: Long?,
    businessId: Long?,
    storeId: Long,
    unitId: Long?,
    createdBy: Long?,
    createdOn: String?,
    modifiedBy: Long?,
    modifiedOn: String?,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = GetProductByProductIdQuery(productId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getString(2)!!,
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5),
      cursor.getLong(6),
      cursor.getDouble(7)!!,
      cursor.getDouble(8),
      cursor.getDouble(9),
      cursor.getLong(10)!!,
      cursor.getString(11),
      cursor.getLong(12)!!,
      cursor.getString(13),
      cursor.getString(14),
      cursor.getString(15),
      cursor.getLong(16),
      cursor.getLong(17),
      cursor.getDouble(18),
      cursor.getLong(19),
      cursor.getLong(20),
      cursor.getLong(21)!!,
      cursor.getLong(22),
      cursor.getLong(23),
      cursor.getString(24),
      cursor.getLong(25),
      cursor.getString(26),
      cursor.getLong(27)!!,
      cursor.getLong(28)!!,
      cursor.getLong(29)!!
    )
  }

  public fun getProductByProductId(productId: Long?): Query<ProductEntity> =
      getProductByProductId(productId) { id, productId_, name, description, additionalInfo,
      category, categoryId, price, billAmount, tax, vat, pic, stock, ingredients, preparationTime,
      servingSize, dailyCapacity, discountType, discountedAmount, brandId, businessId, storeId,
      unitId, createdBy, createdOn, modifiedBy, modifiedOn, createdAt, updatedAt, synced ->
    ProductEntity(
      id,
      productId_,
      name,
      description,
      additionalInfo,
      category,
      categoryId,
      price,
      billAmount,
      tax,
      vat,
      pic,
      stock,
      ingredients,
      preparationTime,
      servingSize,
      dailyCapacity,
      discountType,
      discountedAmount,
      brandId,
      businessId,
      storeId,
      unitId,
      createdBy,
      createdOn,
      modifiedBy,
      modifiedOn,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> getProductsByStore(storeId: Long, mapper: (
    id: Long,
    productId: Long?,
    name: String,
    description: String?,
    additionalInfo: String?,
    category: String?,
    categoryId: Long?,
    price: Double,
    billAmount: Double?,
    tax: Double?,
    vat: Long,
    pic: String?,
    stock: Long,
    ingredients: String?,
    preparationTime: String?,
    servingSize: String?,
    dailyCapacity: Long?,
    discountType: Long?,
    discountedAmount: Double?,
    brandId: Long?,
    businessId: Long?,
    storeId: Long,
    unitId: Long?,
    createdBy: Long?,
    createdOn: String?,
    modifiedBy: Long?,
    modifiedOn: String?,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = GetProductsByStoreQuery(storeId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getString(2)!!,
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5),
      cursor.getLong(6),
      cursor.getDouble(7)!!,
      cursor.getDouble(8),
      cursor.getDouble(9),
      cursor.getLong(10)!!,
      cursor.getString(11),
      cursor.getLong(12)!!,
      cursor.getString(13),
      cursor.getString(14),
      cursor.getString(15),
      cursor.getLong(16),
      cursor.getLong(17),
      cursor.getDouble(18),
      cursor.getLong(19),
      cursor.getLong(20),
      cursor.getLong(21)!!,
      cursor.getLong(22),
      cursor.getLong(23),
      cursor.getString(24),
      cursor.getLong(25),
      cursor.getString(26),
      cursor.getLong(27)!!,
      cursor.getLong(28)!!,
      cursor.getLong(29)!!
    )
  }

  public fun getProductsByStore(storeId: Long): Query<ProductEntity> = getProductsByStore(storeId) {
      id, productId, name, description, additionalInfo, category, categoryId, price, billAmount,
      tax, vat, pic, stock, ingredients, preparationTime, servingSize, dailyCapacity, discountType,
      discountedAmount, brandId, businessId, storeId_, unitId, createdBy, createdOn, modifiedBy,
      modifiedOn, createdAt, updatedAt, synced ->
    ProductEntity(
      id,
      productId,
      name,
      description,
      additionalInfo,
      category,
      categoryId,
      price,
      billAmount,
      tax,
      vat,
      pic,
      stock,
      ingredients,
      preparationTime,
      servingSize,
      dailyCapacity,
      discountType,
      discountedAmount,
      brandId,
      businessId,
      storeId_,
      unitId,
      createdBy,
      createdOn,
      modifiedBy,
      modifiedOn,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> getProductsByCategory(categoryId: Long?, mapper: (
    id: Long,
    productId: Long?,
    name: String,
    description: String?,
    additionalInfo: String?,
    category: String?,
    categoryId: Long?,
    price: Double,
    billAmount: Double?,
    tax: Double?,
    vat: Long,
    pic: String?,
    stock: Long,
    ingredients: String?,
    preparationTime: String?,
    servingSize: String?,
    dailyCapacity: Long?,
    discountType: Long?,
    discountedAmount: Double?,
    brandId: Long?,
    businessId: Long?,
    storeId: Long,
    unitId: Long?,
    createdBy: Long?,
    createdOn: String?,
    modifiedBy: Long?,
    modifiedOn: String?,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = GetProductsByCategoryQuery(categoryId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getString(2)!!,
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5),
      cursor.getLong(6),
      cursor.getDouble(7)!!,
      cursor.getDouble(8),
      cursor.getDouble(9),
      cursor.getLong(10)!!,
      cursor.getString(11),
      cursor.getLong(12)!!,
      cursor.getString(13),
      cursor.getString(14),
      cursor.getString(15),
      cursor.getLong(16),
      cursor.getLong(17),
      cursor.getDouble(18),
      cursor.getLong(19),
      cursor.getLong(20),
      cursor.getLong(21)!!,
      cursor.getLong(22),
      cursor.getLong(23),
      cursor.getString(24),
      cursor.getLong(25),
      cursor.getString(26),
      cursor.getLong(27)!!,
      cursor.getLong(28)!!,
      cursor.getLong(29)!!
    )
  }

  public fun getProductsByCategory(categoryId: Long?): Query<ProductEntity> =
      getProductsByCategory(categoryId) { id, productId, name, description, additionalInfo,
      category, categoryId_, price, billAmount, tax, vat, pic, stock, ingredients, preparationTime,
      servingSize, dailyCapacity, discountType, discountedAmount, brandId, businessId, storeId,
      unitId, createdBy, createdOn, modifiedBy, modifiedOn, createdAt, updatedAt, synced ->
    ProductEntity(
      id,
      productId,
      name,
      description,
      additionalInfo,
      category,
      categoryId_,
      price,
      billAmount,
      tax,
      vat,
      pic,
      stock,
      ingredients,
      preparationTime,
      servingSize,
      dailyCapacity,
      discountType,
      discountedAmount,
      brandId,
      businessId,
      storeId,
      unitId,
      createdBy,
      createdOn,
      modifiedBy,
      modifiedOn,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> getProductsByCategoryAndStore(
    categoryId: Long?,
    storeId: Long,
    mapper: (
      id: Long,
      productId: Long?,
      name: String,
      description: String?,
      additionalInfo: String?,
      category: String?,
      categoryId: Long?,
      price: Double,
      billAmount: Double?,
      tax: Double?,
      vat: Long,
      pic: String?,
      stock: Long,
      ingredients: String?,
      preparationTime: String?,
      servingSize: String?,
      dailyCapacity: Long?,
      discountType: Long?,
      discountedAmount: Double?,
      brandId: Long?,
      businessId: Long?,
      storeId: Long,
      unitId: Long?,
      createdBy: Long?,
      createdOn: String?,
      modifiedBy: Long?,
      modifiedOn: String?,
      createdAt: Long,
      updatedAt: Long,
      synced: Long,
    ) -> T,
  ): Query<T> = GetProductsByCategoryAndStoreQuery(categoryId, storeId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getString(2)!!,
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5),
      cursor.getLong(6),
      cursor.getDouble(7)!!,
      cursor.getDouble(8),
      cursor.getDouble(9),
      cursor.getLong(10)!!,
      cursor.getString(11),
      cursor.getLong(12)!!,
      cursor.getString(13),
      cursor.getString(14),
      cursor.getString(15),
      cursor.getLong(16),
      cursor.getLong(17),
      cursor.getDouble(18),
      cursor.getLong(19),
      cursor.getLong(20),
      cursor.getLong(21)!!,
      cursor.getLong(22),
      cursor.getLong(23),
      cursor.getString(24),
      cursor.getLong(25),
      cursor.getString(26),
      cursor.getLong(27)!!,
      cursor.getLong(28)!!,
      cursor.getLong(29)!!
    )
  }

  public fun getProductsByCategoryAndStore(categoryId: Long?, storeId: Long): Query<ProductEntity> =
      getProductsByCategoryAndStore(categoryId, storeId) { id, productId, name, description,
      additionalInfo, category, categoryId_, price, billAmount, tax, vat, pic, stock, ingredients,
      preparationTime, servingSize, dailyCapacity, discountType, discountedAmount, brandId,
      businessId, storeId_, unitId, createdBy, createdOn, modifiedBy, modifiedOn, createdAt,
      updatedAt, synced ->
    ProductEntity(
      id,
      productId,
      name,
      description,
      additionalInfo,
      category,
      categoryId_,
      price,
      billAmount,
      tax,
      vat,
      pic,
      stock,
      ingredients,
      preparationTime,
      servingSize,
      dailyCapacity,
      discountType,
      discountedAmount,
      brandId,
      businessId,
      storeId_,
      unitId,
      createdBy,
      createdOn,
      modifiedBy,
      modifiedOn,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> getInStockProducts(mapper: (
    id: Long,
    productId: Long?,
    name: String,
    description: String?,
    additionalInfo: String?,
    category: String?,
    categoryId: Long?,
    price: Double,
    billAmount: Double?,
    tax: Double?,
    vat: Long,
    pic: String?,
    stock: Long,
    ingredients: String?,
    preparationTime: String?,
    servingSize: String?,
    dailyCapacity: Long?,
    discountType: Long?,
    discountedAmount: Double?,
    brandId: Long?,
    businessId: Long?,
    storeId: Long,
    unitId: Long?,
    createdBy: Long?,
    createdOn: String?,
    modifiedBy: Long?,
    modifiedOn: String?,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = Query(840_624_280, arrayOf("ProductEntity"), driver, "Product.sq",
      "getInStockProducts",
      "SELECT ProductEntity.id, ProductEntity.productId, ProductEntity.name, ProductEntity.description, ProductEntity.additionalInfo, ProductEntity.category, ProductEntity.categoryId, ProductEntity.price, ProductEntity.billAmount, ProductEntity.tax, ProductEntity.vat, ProductEntity.pic, ProductEntity.stock, ProductEntity.ingredients, ProductEntity.preparationTime, ProductEntity.servingSize, ProductEntity.dailyCapacity, ProductEntity.discountType, ProductEntity.discountedAmount, ProductEntity.brandId, ProductEntity.businessId, ProductEntity.storeId, ProductEntity.unitId, ProductEntity.createdBy, ProductEntity.createdOn, ProductEntity.modifiedBy, ProductEntity.modifiedOn, ProductEntity.createdAt, ProductEntity.updatedAt, ProductEntity.synced FROM ProductEntity WHERE stock = 1 ORDER BY name ASC") {
      cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getString(2)!!,
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5),
      cursor.getLong(6),
      cursor.getDouble(7)!!,
      cursor.getDouble(8),
      cursor.getDouble(9),
      cursor.getLong(10)!!,
      cursor.getString(11),
      cursor.getLong(12)!!,
      cursor.getString(13),
      cursor.getString(14),
      cursor.getString(15),
      cursor.getLong(16),
      cursor.getLong(17),
      cursor.getDouble(18),
      cursor.getLong(19),
      cursor.getLong(20),
      cursor.getLong(21)!!,
      cursor.getLong(22),
      cursor.getLong(23),
      cursor.getString(24),
      cursor.getLong(25),
      cursor.getString(26),
      cursor.getLong(27)!!,
      cursor.getLong(28)!!,
      cursor.getLong(29)!!
    )
  }

  public fun getInStockProducts(): Query<ProductEntity> = getInStockProducts { id, productId, name,
      description, additionalInfo, category, categoryId, price, billAmount, tax, vat, pic, stock,
      ingredients, preparationTime, servingSize, dailyCapacity, discountType, discountedAmount,
      brandId, businessId, storeId, unitId, createdBy, createdOn, modifiedBy, modifiedOn, createdAt,
      updatedAt, synced ->
    ProductEntity(
      id,
      productId,
      name,
      description,
      additionalInfo,
      category,
      categoryId,
      price,
      billAmount,
      tax,
      vat,
      pic,
      stock,
      ingredients,
      preparationTime,
      servingSize,
      dailyCapacity,
      discountType,
      discountedAmount,
      brandId,
      businessId,
      storeId,
      unitId,
      createdBy,
      createdOn,
      modifiedBy,
      modifiedOn,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> getInStockProductsByStore(storeId: Long, mapper: (
    id: Long,
    productId: Long?,
    name: String,
    description: String?,
    additionalInfo: String?,
    category: String?,
    categoryId: Long?,
    price: Double,
    billAmount: Double?,
    tax: Double?,
    vat: Long,
    pic: String?,
    stock: Long,
    ingredients: String?,
    preparationTime: String?,
    servingSize: String?,
    dailyCapacity: Long?,
    discountType: Long?,
    discountedAmount: Double?,
    brandId: Long?,
    businessId: Long?,
    storeId: Long,
    unitId: Long?,
    createdBy: Long?,
    createdOn: String?,
    modifiedBy: Long?,
    modifiedOn: String?,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = GetInStockProductsByStoreQuery(storeId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getString(2)!!,
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5),
      cursor.getLong(6),
      cursor.getDouble(7)!!,
      cursor.getDouble(8),
      cursor.getDouble(9),
      cursor.getLong(10)!!,
      cursor.getString(11),
      cursor.getLong(12)!!,
      cursor.getString(13),
      cursor.getString(14),
      cursor.getString(15),
      cursor.getLong(16),
      cursor.getLong(17),
      cursor.getDouble(18),
      cursor.getLong(19),
      cursor.getLong(20),
      cursor.getLong(21)!!,
      cursor.getLong(22),
      cursor.getLong(23),
      cursor.getString(24),
      cursor.getLong(25),
      cursor.getString(26),
      cursor.getLong(27)!!,
      cursor.getLong(28)!!,
      cursor.getLong(29)!!
    )
  }

  public fun getInStockProductsByStore(storeId: Long): Query<ProductEntity> =
      getInStockProductsByStore(storeId) { id, productId, name, description, additionalInfo,
      category, categoryId, price, billAmount, tax, vat, pic, stock, ingredients, preparationTime,
      servingSize, dailyCapacity, discountType, discountedAmount, brandId, businessId, storeId_,
      unitId, createdBy, createdOn, modifiedBy, modifiedOn, createdAt, updatedAt, synced ->
    ProductEntity(
      id,
      productId,
      name,
      description,
      additionalInfo,
      category,
      categoryId,
      price,
      billAmount,
      tax,
      vat,
      pic,
      stock,
      ingredients,
      preparationTime,
      servingSize,
      dailyCapacity,
      discountType,
      discountedAmount,
      brandId,
      businessId,
      storeId_,
      unitId,
      createdBy,
      createdOn,
      modifiedBy,
      modifiedOn,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> getProductsByStockStatus(stock: Long, mapper: (
    id: Long,
    productId: Long?,
    name: String,
    description: String?,
    additionalInfo: String?,
    category: String?,
    categoryId: Long?,
    price: Double,
    billAmount: Double?,
    tax: Double?,
    vat: Long,
    pic: String?,
    stock: Long,
    ingredients: String?,
    preparationTime: String?,
    servingSize: String?,
    dailyCapacity: Long?,
    discountType: Long?,
    discountedAmount: Double?,
    brandId: Long?,
    businessId: Long?,
    storeId: Long,
    unitId: Long?,
    createdBy: Long?,
    createdOn: String?,
    modifiedBy: Long?,
    modifiedOn: String?,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = GetProductsByStockStatusQuery(stock) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getString(2)!!,
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5),
      cursor.getLong(6),
      cursor.getDouble(7)!!,
      cursor.getDouble(8),
      cursor.getDouble(9),
      cursor.getLong(10)!!,
      cursor.getString(11),
      cursor.getLong(12)!!,
      cursor.getString(13),
      cursor.getString(14),
      cursor.getString(15),
      cursor.getLong(16),
      cursor.getLong(17),
      cursor.getDouble(18),
      cursor.getLong(19),
      cursor.getLong(20),
      cursor.getLong(21)!!,
      cursor.getLong(22),
      cursor.getLong(23),
      cursor.getString(24),
      cursor.getLong(25),
      cursor.getString(26),
      cursor.getLong(27)!!,
      cursor.getLong(28)!!,
      cursor.getLong(29)!!
    )
  }

  public fun getProductsByStockStatus(stock: Long): Query<ProductEntity> =
      getProductsByStockStatus(stock) { id, productId, name, description, additionalInfo, category,
      categoryId, price, billAmount, tax, vat, pic, stock_, ingredients, preparationTime,
      servingSize, dailyCapacity, discountType, discountedAmount, brandId, businessId, storeId,
      unitId, createdBy, createdOn, modifiedBy, modifiedOn, createdAt, updatedAt, synced ->
    ProductEntity(
      id,
      productId,
      name,
      description,
      additionalInfo,
      category,
      categoryId,
      price,
      billAmount,
      tax,
      vat,
      pic,
      stock_,
      ingredients,
      preparationTime,
      servingSize,
      dailyCapacity,
      discountType,
      discountedAmount,
      brandId,
      businessId,
      storeId,
      unitId,
      createdBy,
      createdOn,
      modifiedBy,
      modifiedOn,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> searchProductsByName(`value`: String, mapper: (
    id: Long,
    productId: Long?,
    name: String,
    description: String?,
    additionalInfo: String?,
    category: String?,
    categoryId: Long?,
    price: Double,
    billAmount: Double?,
    tax: Double?,
    vat: Long,
    pic: String?,
    stock: Long,
    ingredients: String?,
    preparationTime: String?,
    servingSize: String?,
    dailyCapacity: Long?,
    discountType: Long?,
    discountedAmount: Double?,
    brandId: Long?,
    businessId: Long?,
    storeId: Long,
    unitId: Long?,
    createdBy: Long?,
    createdOn: String?,
    modifiedBy: Long?,
    modifiedOn: String?,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = SearchProductsByNameQuery(value) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getString(2)!!,
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5),
      cursor.getLong(6),
      cursor.getDouble(7)!!,
      cursor.getDouble(8),
      cursor.getDouble(9),
      cursor.getLong(10)!!,
      cursor.getString(11),
      cursor.getLong(12)!!,
      cursor.getString(13),
      cursor.getString(14),
      cursor.getString(15),
      cursor.getLong(16),
      cursor.getLong(17),
      cursor.getDouble(18),
      cursor.getLong(19),
      cursor.getLong(20),
      cursor.getLong(21)!!,
      cursor.getLong(22),
      cursor.getLong(23),
      cursor.getString(24),
      cursor.getLong(25),
      cursor.getString(26),
      cursor.getLong(27)!!,
      cursor.getLong(28)!!,
      cursor.getLong(29)!!
    )
  }

  public fun searchProductsByName(value_: String): Query<ProductEntity> =
      searchProductsByName(value_) { id, productId, name, description, additionalInfo, category,
      categoryId, price, billAmount, tax, vat, pic, stock, ingredients, preparationTime,
      servingSize, dailyCapacity, discountType, discountedAmount, brandId, businessId, storeId,
      unitId, createdBy, createdOn, modifiedBy, modifiedOn, createdAt, updatedAt, synced ->
    ProductEntity(
      id,
      productId,
      name,
      description,
      additionalInfo,
      category,
      categoryId,
      price,
      billAmount,
      tax,
      vat,
      pic,
      stock,
      ingredients,
      preparationTime,
      servingSize,
      dailyCapacity,
      discountType,
      discountedAmount,
      brandId,
      businessId,
      storeId,
      unitId,
      createdBy,
      createdOn,
      modifiedBy,
      modifiedOn,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> searchProductsByNameAndStore(
    `value`: String,
    storeId: Long,
    mapper: (
      id: Long,
      productId: Long?,
      name: String,
      description: String?,
      additionalInfo: String?,
      category: String?,
      categoryId: Long?,
      price: Double,
      billAmount: Double?,
      tax: Double?,
      vat: Long,
      pic: String?,
      stock: Long,
      ingredients: String?,
      preparationTime: String?,
      servingSize: String?,
      dailyCapacity: Long?,
      discountType: Long?,
      discountedAmount: Double?,
      brandId: Long?,
      businessId: Long?,
      storeId: Long,
      unitId: Long?,
      createdBy: Long?,
      createdOn: String?,
      modifiedBy: Long?,
      modifiedOn: String?,
      createdAt: Long,
      updatedAt: Long,
      synced: Long,
    ) -> T,
  ): Query<T> = SearchProductsByNameAndStoreQuery(value, storeId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getString(2)!!,
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5),
      cursor.getLong(6),
      cursor.getDouble(7)!!,
      cursor.getDouble(8),
      cursor.getDouble(9),
      cursor.getLong(10)!!,
      cursor.getString(11),
      cursor.getLong(12)!!,
      cursor.getString(13),
      cursor.getString(14),
      cursor.getString(15),
      cursor.getLong(16),
      cursor.getLong(17),
      cursor.getDouble(18),
      cursor.getLong(19),
      cursor.getLong(20),
      cursor.getLong(21)!!,
      cursor.getLong(22),
      cursor.getLong(23),
      cursor.getString(24),
      cursor.getLong(25),
      cursor.getString(26),
      cursor.getLong(27)!!,
      cursor.getLong(28)!!,
      cursor.getLong(29)!!
    )
  }

  public fun searchProductsByNameAndStore(value_: String, storeId: Long): Query<ProductEntity> =
      searchProductsByNameAndStore(value_, storeId) { id, productId, name, description,
      additionalInfo, category, categoryId, price, billAmount, tax, vat, pic, stock, ingredients,
      preparationTime, servingSize, dailyCapacity, discountType, discountedAmount, brandId,
      businessId, storeId_, unitId, createdBy, createdOn, modifiedBy, modifiedOn, createdAt,
      updatedAt, synced ->
    ProductEntity(
      id,
      productId,
      name,
      description,
      additionalInfo,
      category,
      categoryId,
      price,
      billAmount,
      tax,
      vat,
      pic,
      stock,
      ingredients,
      preparationTime,
      servingSize,
      dailyCapacity,
      discountType,
      discountedAmount,
      brandId,
      businessId,
      storeId_,
      unitId,
      createdBy,
      createdOn,
      modifiedBy,
      modifiedOn,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> getProductCategories(mapper: (category: String, categoryId: Long?) -> T):
      Query<T> = Query(1_073_307_342, arrayOf("ProductEntity"), driver, "Product.sq",
      "getProductCategories",
      "SELECT DISTINCT category, categoryId FROM ProductEntity WHERE category IS NOT NULL ORDER BY category ASC") {
      cursor ->
    mapper(
      cursor.getString(0)!!,
      cursor.getLong(1)
    )
  }

  public fun getProductCategories(): Query<GetProductCategories> = getProductCategories { category,
      categoryId ->
    GetProductCategories(
      category,
      categoryId
    )
  }

  public fun <T : Any> getProductCategoriesByStore(storeId: Long, mapper: (category: String,
      categoryId: Long?) -> T): Query<T> = GetProductCategoriesByStoreQuery(storeId) { cursor ->
    mapper(
      cursor.getString(0)!!,
      cursor.getLong(1)
    )
  }

  public fun getProductCategoriesByStore(storeId: Long): Query<GetProductCategoriesByStore> =
      getProductCategoriesByStore(storeId) { category, categoryId ->
    GetProductCategoriesByStore(
      category,
      categoryId
    )
  }

  public fun <T : Any> getUnsyncedProducts(mapper: (
    id: Long,
    productId: Long?,
    name: String,
    description: String?,
    additionalInfo: String?,
    category: String?,
    categoryId: Long?,
    price: Double,
    billAmount: Double?,
    tax: Double?,
    vat: Long,
    pic: String?,
    stock: Long,
    ingredients: String?,
    preparationTime: String?,
    servingSize: String?,
    dailyCapacity: Long?,
    discountType: Long?,
    discountedAmount: Double?,
    brandId: Long?,
    businessId: Long?,
    storeId: Long,
    unitId: Long?,
    createdBy: Long?,
    createdOn: String?,
    modifiedBy: Long?,
    modifiedOn: String?,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = Query(1_089_974_292, arrayOf("ProductEntity"), driver, "Product.sq",
      "getUnsyncedProducts",
      "SELECT ProductEntity.id, ProductEntity.productId, ProductEntity.name, ProductEntity.description, ProductEntity.additionalInfo, ProductEntity.category, ProductEntity.categoryId, ProductEntity.price, ProductEntity.billAmount, ProductEntity.tax, ProductEntity.vat, ProductEntity.pic, ProductEntity.stock, ProductEntity.ingredients, ProductEntity.preparationTime, ProductEntity.servingSize, ProductEntity.dailyCapacity, ProductEntity.discountType, ProductEntity.discountedAmount, ProductEntity.brandId, ProductEntity.businessId, ProductEntity.storeId, ProductEntity.unitId, ProductEntity.createdBy, ProductEntity.createdOn, ProductEntity.modifiedBy, ProductEntity.modifiedOn, ProductEntity.createdAt, ProductEntity.updatedAt, ProductEntity.synced FROM ProductEntity WHERE synced = 0 ORDER BY createdAt ASC") {
      cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getString(2)!!,
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5),
      cursor.getLong(6),
      cursor.getDouble(7)!!,
      cursor.getDouble(8),
      cursor.getDouble(9),
      cursor.getLong(10)!!,
      cursor.getString(11),
      cursor.getLong(12)!!,
      cursor.getString(13),
      cursor.getString(14),
      cursor.getString(15),
      cursor.getLong(16),
      cursor.getLong(17),
      cursor.getDouble(18),
      cursor.getLong(19),
      cursor.getLong(20),
      cursor.getLong(21)!!,
      cursor.getLong(22),
      cursor.getLong(23),
      cursor.getString(24),
      cursor.getLong(25),
      cursor.getString(26),
      cursor.getLong(27)!!,
      cursor.getLong(28)!!,
      cursor.getLong(29)!!
    )
  }

  public fun getUnsyncedProducts(): Query<ProductEntity> = getUnsyncedProducts { id, productId,
      name, description, additionalInfo, category, categoryId, price, billAmount, tax, vat, pic,
      stock, ingredients, preparationTime, servingSize, dailyCapacity, discountType,
      discountedAmount, brandId, businessId, storeId, unitId, createdBy, createdOn, modifiedBy,
      modifiedOn, createdAt, updatedAt, synced ->
    ProductEntity(
      id,
      productId,
      name,
      description,
      additionalInfo,
      category,
      categoryId,
      price,
      billAmount,
      tax,
      vat,
      pic,
      stock,
      ingredients,
      preparationTime,
      servingSize,
      dailyCapacity,
      discountType,
      discountedAmount,
      brandId,
      businessId,
      storeId,
      unitId,
      createdBy,
      createdOn,
      modifiedBy,
      modifiedOn,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> getSyncedProducts(mapper: (
    id: Long,
    productId: Long?,
    name: String,
    description: String?,
    additionalInfo: String?,
    category: String?,
    categoryId: Long?,
    price: Double,
    billAmount: Double?,
    tax: Double?,
    vat: Long,
    pic: String?,
    stock: Long,
    ingredients: String?,
    preparationTime: String?,
    servingSize: String?,
    dailyCapacity: Long?,
    discountType: Long?,
    discountedAmount: Double?,
    brandId: Long?,
    businessId: Long?,
    storeId: Long,
    unitId: Long?,
    createdBy: Long?,
    createdOn: String?,
    modifiedBy: Long?,
    modifiedOn: String?,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = Query(-1_293_628_293, arrayOf("ProductEntity"), driver, "Product.sq",
      "getSyncedProducts",
      "SELECT ProductEntity.id, ProductEntity.productId, ProductEntity.name, ProductEntity.description, ProductEntity.additionalInfo, ProductEntity.category, ProductEntity.categoryId, ProductEntity.price, ProductEntity.billAmount, ProductEntity.tax, ProductEntity.vat, ProductEntity.pic, ProductEntity.stock, ProductEntity.ingredients, ProductEntity.preparationTime, ProductEntity.servingSize, ProductEntity.dailyCapacity, ProductEntity.discountType, ProductEntity.discountedAmount, ProductEntity.brandId, ProductEntity.businessId, ProductEntity.storeId, ProductEntity.unitId, ProductEntity.createdBy, ProductEntity.createdOn, ProductEntity.modifiedBy, ProductEntity.modifiedOn, ProductEntity.createdAt, ProductEntity.updatedAt, ProductEntity.synced FROM ProductEntity WHERE synced = 1 ORDER BY name ASC") {
      cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getString(2)!!,
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5),
      cursor.getLong(6),
      cursor.getDouble(7)!!,
      cursor.getDouble(8),
      cursor.getDouble(9),
      cursor.getLong(10)!!,
      cursor.getString(11),
      cursor.getLong(12)!!,
      cursor.getString(13),
      cursor.getString(14),
      cursor.getString(15),
      cursor.getLong(16),
      cursor.getLong(17),
      cursor.getDouble(18),
      cursor.getLong(19),
      cursor.getLong(20),
      cursor.getLong(21)!!,
      cursor.getLong(22),
      cursor.getLong(23),
      cursor.getString(24),
      cursor.getLong(25),
      cursor.getString(26),
      cursor.getLong(27)!!,
      cursor.getLong(28)!!,
      cursor.getLong(29)!!
    )
  }

  public fun getSyncedProducts(): Query<ProductEntity> = getSyncedProducts { id, productId, name,
      description, additionalInfo, category, categoryId, price, billAmount, tax, vat, pic, stock,
      ingredients, preparationTime, servingSize, dailyCapacity, discountType, discountedAmount,
      brandId, businessId, storeId, unitId, createdBy, createdOn, modifiedBy, modifiedOn, createdAt,
      updatedAt, synced ->
    ProductEntity(
      id,
      productId,
      name,
      description,
      additionalInfo,
      category,
      categoryId,
      price,
      billAmount,
      tax,
      vat,
      pic,
      stock,
      ingredients,
      preparationTime,
      servingSize,
      dailyCapacity,
      discountType,
      discountedAmount,
      brandId,
      businessId,
      storeId,
      unitId,
      createdBy,
      createdOn,
      modifiedBy,
      modifiedOn,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun getProductCount(): Query<Long> = Query(-1_292_824_291, arrayOf("ProductEntity"),
      driver, "Product.sq", "getProductCount", "SELECT COUNT(*) FROM ProductEntity") { cursor ->
    cursor.getLong(0)!!
  }

  public fun getProductCountByStore(storeId: Long): Query<Long> =
      GetProductCountByStoreQuery(storeId) { cursor ->
    cursor.getLong(0)!!
  }

  public fun getInStockProductCount(): Query<Long> = Query(1_616_162_452, arrayOf("ProductEntity"),
      driver, "Product.sq", "getInStockProductCount",
      "SELECT COUNT(*) FROM ProductEntity WHERE stock = 1") { cursor ->
    cursor.getLong(0)!!
  }

  public fun getInStockProductCountByStore(storeId: Long): Query<Long> =
      GetInStockProductCountByStoreQuery(storeId) { cursor ->
    cursor.getLong(0)!!
  }

  public fun getUnsyncedProductCount(): Query<Long> = Query(-1_672_914_928,
      arrayOf("ProductEntity"), driver, "Product.sq", "getUnsyncedProductCount",
      "SELECT COUNT(*) FROM ProductEntity WHERE synced = 0") { cursor ->
    cursor.getLong(0)!!
  }

  public fun <T : Any> getProductsPaginated(
    storeId: Long,
    `value`: Long,
    value_: Long,
    mapper: (
      id: Long,
      productId: Long?,
      name: String,
      description: String?,
      additionalInfo: String?,
      category: String?,
      categoryId: Long?,
      price: Double,
      billAmount: Double?,
      tax: Double?,
      vat: Long,
      pic: String?,
      stock: Long,
      ingredients: String?,
      preparationTime: String?,
      servingSize: String?,
      dailyCapacity: Long?,
      discountType: Long?,
      discountedAmount: Double?,
      brandId: Long?,
      businessId: Long?,
      storeId: Long,
      unitId: Long?,
      createdBy: Long?,
      createdOn: String?,
      modifiedBy: Long?,
      modifiedOn: String?,
      createdAt: Long,
      updatedAt: Long,
      synced: Long,
    ) -> T,
  ): Query<T> = GetProductsPaginatedQuery(storeId, value, value_) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getString(2)!!,
      cursor.getString(3),
      cursor.getString(4),
      cursor.getString(5),
      cursor.getLong(6),
      cursor.getDouble(7)!!,
      cursor.getDouble(8),
      cursor.getDouble(9),
      cursor.getLong(10)!!,
      cursor.getString(11),
      cursor.getLong(12)!!,
      cursor.getString(13),
      cursor.getString(14),
      cursor.getString(15),
      cursor.getLong(16),
      cursor.getLong(17),
      cursor.getDouble(18),
      cursor.getLong(19),
      cursor.getLong(20),
      cursor.getLong(21)!!,
      cursor.getLong(22),
      cursor.getLong(23),
      cursor.getString(24),
      cursor.getLong(25),
      cursor.getString(26),
      cursor.getLong(27)!!,
      cursor.getLong(28)!!,
      cursor.getLong(29)!!
    )
  }

  public fun getProductsPaginated(
    storeId: Long,
    value_: Long,
    value__: Long,
  ): Query<ProductEntity> = getProductsPaginated(storeId, value_, value__) { id, productId, name,
      description, additionalInfo, category, categoryId, price, billAmount, tax, vat, pic, stock,
      ingredients, preparationTime, servingSize, dailyCapacity, discountType, discountedAmount,
      brandId, businessId, storeId_, unitId, createdBy, createdOn, modifiedBy, modifiedOn,
      createdAt, updatedAt, synced ->
    ProductEntity(
      id,
      productId,
      name,
      description,
      additionalInfo,
      category,
      categoryId,
      price,
      billAmount,
      tax,
      vat,
      pic,
      stock,
      ingredients,
      preparationTime,
      servingSize,
      dailyCapacity,
      discountType,
      discountedAmount,
      brandId,
      businessId,
      storeId_,
      unitId,
      createdBy,
      createdOn,
      modifiedBy,
      modifiedOn,
      createdAt,
      updatedAt,
      synced
    )
  }

  /**
   * @return The number of rows updated.
   */
  public fun insertProduct(
    productId: Long?,
    name: String,
    description: String?,
    additionalInfo: String?,
    category: String?,
    categoryId: Long?,
    price: Double,
    billAmount: Double?,
    tax: Double?,
    vat: Long,
    pic: String?,
    stock: Long,
    ingredients: String?,
    preparationTime: String?,
    servingSize: String?,
    dailyCapacity: Long?,
    discountType: Long?,
    discountedAmount: Double?,
    brandId: Long?,
    businessId: Long?,
    storeId: Long,
    unitId: Long?,
    createdBy: Long?,
    createdOn: String?,
    modifiedBy: Long?,
    modifiedOn: String?,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ): QueryResult<Long> {
    val result = driver.execute(-1_718_902_019, """
        |INSERT INTO ProductEntity (
        |    productId, name, description, additionalInfo, category, categoryId,
        |    price, billAmount, tax, vat, pic, stock, ingredients, preparationTime,
        |    servingSize, dailyCapacity, discountType, discountedAmount, brandId,
        |    businessId, storeId, unitId, createdBy, createdOn, modifiedBy,
        |    modifiedOn, createdAt, updatedAt, synced
        |) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """.trimMargin(), 29) {
          bindLong(0, productId)
          bindString(1, name)
          bindString(2, description)
          bindString(3, additionalInfo)
          bindString(4, category)
          bindLong(5, categoryId)
          bindDouble(6, price)
          bindDouble(7, billAmount)
          bindDouble(8, tax)
          bindLong(9, vat)
          bindString(10, pic)
          bindLong(11, stock)
          bindString(12, ingredients)
          bindString(13, preparationTime)
          bindString(14, servingSize)
          bindLong(15, dailyCapacity)
          bindLong(16, discountType)
          bindDouble(17, discountedAmount)
          bindLong(18, brandId)
          bindLong(19, businessId)
          bindLong(20, storeId)
          bindLong(21, unitId)
          bindLong(22, createdBy)
          bindString(23, createdOn)
          bindLong(24, modifiedBy)
          bindString(25, modifiedOn)
          bindLong(26, createdAt)
          bindLong(27, updatedAt)
          bindLong(28, synced)
        }
    notifyQueries(-1_718_902_019) { emit ->
      emit("ProductEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun insertOrReplaceProduct(
    productId: Long?,
    name: String,
    description: String?,
    additionalInfo: String?,
    category: String?,
    categoryId: Long?,
    price: Double,
    billAmount: Double?,
    tax: Double?,
    vat: Long,
    pic: String?,
    stock: Long,
    ingredients: String?,
    preparationTime: String?,
    servingSize: String?,
    dailyCapacity: Long?,
    discountType: Long?,
    discountedAmount: Double?,
    brandId: Long?,
    businessId: Long?,
    storeId: Long,
    unitId: Long?,
    createdBy: Long?,
    createdOn: String?,
    modifiedBy: Long?,
    modifiedOn: String?,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ): QueryResult<Long> {
    val result = driver.execute(-419_206_640, """
        |INSERT OR REPLACE INTO ProductEntity (
        |    productId, name, description, additionalInfo, category, categoryId,
        |    price, billAmount, tax, vat, pic, stock, ingredients, preparationTime,
        |    servingSize, dailyCapacity, discountType, discountedAmount, brandId,
        |    businessId, storeId, unitId, createdBy, createdOn, modifiedBy,
        |    modifiedOn, createdAt, updatedAt, synced
        |) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """.trimMargin(), 29) {
          bindLong(0, productId)
          bindString(1, name)
          bindString(2, description)
          bindString(3, additionalInfo)
          bindString(4, category)
          bindLong(5, categoryId)
          bindDouble(6, price)
          bindDouble(7, billAmount)
          bindDouble(8, tax)
          bindLong(9, vat)
          bindString(10, pic)
          bindLong(11, stock)
          bindString(12, ingredients)
          bindString(13, preparationTime)
          bindString(14, servingSize)
          bindLong(15, dailyCapacity)
          bindLong(16, discountType)
          bindDouble(17, discountedAmount)
          bindLong(18, brandId)
          bindLong(19, businessId)
          bindLong(20, storeId)
          bindLong(21, unitId)
          bindLong(22, createdBy)
          bindString(23, createdOn)
          bindLong(24, modifiedBy)
          bindString(25, modifiedOn)
          bindLong(26, createdAt)
          bindLong(27, updatedAt)
          bindLong(28, synced)
        }
    notifyQueries(-419_206_640) { emit ->
      emit("ProductEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun updateProduct(
    name: String,
    description: String?,
    additionalInfo: String?,
    category: String?,
    categoryId: Long?,
    price: Double,
    billAmount: Double?,
    tax: Double?,
    vat: Long,
    pic: String?,
    stock: Long,
    ingredients: String?,
    preparationTime: String?,
    servingSize: String?,
    dailyCapacity: Long?,
    discountType: Long?,
    discountedAmount: Double?,
    brandId: Long?,
    businessId: Long?,
    unitId: Long?,
    modifiedBy: Long?,
    modifiedOn: String?,
    updatedAt: Long,
    id: Long,
  ): QueryResult<Long> {
    val result = driver.execute(1_591_622_381, """
        |UPDATE ProductEntity 
        |SET name = ?, description = ?, additionalInfo = ?, category = ?, categoryId = ?,
        |    price = ?, billAmount = ?, tax = ?, vat = ?, pic = ?, stock = ?,
        |    ingredients = ?, preparationTime = ?, servingSize = ?, dailyCapacity = ?,
        |    discountType = ?, discountedAmount = ?, brandId = ?, businessId = ?,
        |    unitId = ?, modifiedBy = ?, modifiedOn = ?, updatedAt = ?
        |WHERE id = ?
        """.trimMargin(), 24) {
          bindString(0, name)
          bindString(1, description)
          bindString(2, additionalInfo)
          bindString(3, category)
          bindLong(4, categoryId)
          bindDouble(5, price)
          bindDouble(6, billAmount)
          bindDouble(7, tax)
          bindLong(8, vat)
          bindString(9, pic)
          bindLong(10, stock)
          bindString(11, ingredients)
          bindString(12, preparationTime)
          bindString(13, servingSize)
          bindLong(14, dailyCapacity)
          bindLong(15, discountType)
          bindDouble(16, discountedAmount)
          bindLong(17, brandId)
          bindLong(18, businessId)
          bindLong(19, unitId)
          bindLong(20, modifiedBy)
          bindString(21, modifiedOn)
          bindLong(22, updatedAt)
          bindLong(23, id)
        }
    notifyQueries(1_591_622_381) { emit ->
      emit("ProductEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun updateProductStock(
    stock: Long,
    updatedAt: Long,
    id: Long,
  ): QueryResult<Long> {
    val result = driver.execute(-836_069_335, """
        |UPDATE ProductEntity 
        |SET stock = ?, updatedAt = ?
        |WHERE id = ?
        """.trimMargin(), 3) {
          bindLong(0, stock)
          bindLong(1, updatedAt)
          bindLong(2, id)
        }
    notifyQueries(-836_069_335) { emit ->
      emit("ProductEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun updateProductStockByProductId(
    stock: Long,
    updatedAt: Long,
    productId: Long?,
  ): QueryResult<Long> {
    val result = driver.execute(null, """
        |UPDATE ProductEntity 
        |SET stock = ?, updatedAt = ?
        |WHERE productId ${ if (productId == null) "IS" else "=" } ?
        """.trimMargin(), 3) {
          bindLong(0, stock)
          bindLong(1, updatedAt)
          bindLong(2, productId)
        }
    notifyQueries(1_537_665_002) { emit ->
      emit("ProductEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun updateProductPrice(
    price: Double,
    updatedAt: Long,
    id: Long,
  ): QueryResult<Long> {
    val result = driver.execute(-838_905_252, """
        |UPDATE ProductEntity 
        |SET price = ?, updatedAt = ?
        |WHERE id = ?
        """.trimMargin(), 3) {
          bindDouble(0, price)
          bindLong(1, updatedAt)
          bindLong(2, id)
        }
    notifyQueries(-838_905_252) { emit ->
      emit("ProductEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun markProductSynced(updatedAt: Long, id: Long): QueryResult<Long> {
    val result = driver.execute(1_162_955_779, """
        |UPDATE ProductEntity 
        |SET synced = 1, updatedAt = ?
        |WHERE id = ?
        """.trimMargin(), 2) {
          bindLong(0, updatedAt)
          bindLong(1, id)
        }
    notifyQueries(1_162_955_779) { emit ->
      emit("ProductEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun markProductUnsynced(updatedAt: Long, id: Long): QueryResult<Long> {
    val result = driver.execute(-909_212_260, """
        |UPDATE ProductEntity 
        |SET synced = 0, updatedAt = ?
        |WHERE id = ?
        """.trimMargin(), 2) {
          bindLong(0, updatedAt)
          bindLong(1, id)
        }
    notifyQueries(-909_212_260) { emit ->
      emit("ProductEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun markProductsSyncedByProductIds(updatedAt: Long, productId: Collection<Long?>):
      QueryResult<Long> {
    val productIdIndexes = createArguments(count = productId.size)
    val result = driver.execute(null, """
        |UPDATE ProductEntity 
        |SET synced = 1, updatedAt = ?
        |WHERE productId IN $productIdIndexes
        """.trimMargin(), 1 + productId.size) {
          bindLong(0, updatedAt)
          productId.forEachIndexed { index, productId_ ->
            bindLong(index + 1, productId_)
          }
        }
    notifyQueries(1_753_365_924) { emit ->
      emit("ProductEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun deleteProduct(id: Long): QueryResult<Long> {
    val result = driver.execute(-1_412_166_709, """DELETE FROM ProductEntity WHERE id = ?""", 1) {
          bindLong(0, id)
        }
    notifyQueries(-1_412_166_709) { emit ->
      emit("ProductEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun deleteProductByProductId(productId: Long?): QueryResult<Long> {
    val result = driver.execute(null,
        """DELETE FROM ProductEntity WHERE productId ${ if (productId == null) "IS" else "=" } ?""",
        1) {
          bindLong(0, productId)
        }
    notifyQueries(907_164_424) { emit ->
      emit("ProductEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun deleteProductsByStore(storeId: Long): QueryResult<Long> {
    val result = driver.execute(1_000_467_362, """DELETE FROM ProductEntity WHERE storeId = ?""", 1)
        {
          bindLong(0, storeId)
        }
    notifyQueries(1_000_467_362) { emit ->
      emit("ProductEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun insertMultipleProducts(ProductEntity: ProductEntity): QueryResult<Long> {
    val result = driver.execute(1_551_295_814, """
        |INSERT INTO ProductEntity (
        |    productId, name, description, additionalInfo, category, categoryId,
        |    price, billAmount, tax, vat, pic, stock, ingredients, preparationTime,
        |    servingSize, dailyCapacity, discountType, discountedAmount, brandId,
        |    businessId, storeId, unitId, createdBy, createdOn, modifiedBy,
        |    modifiedOn, createdAt, updatedAt, synced
        |) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """.trimMargin(), 29) {
          bindLong(0, ProductEntity.productId)
          bindString(1, ProductEntity.name)
          bindString(2, ProductEntity.description)
          bindString(3, ProductEntity.additionalInfo)
          bindString(4, ProductEntity.category)
          bindLong(5, ProductEntity.categoryId)
          bindDouble(6, ProductEntity.price)
          bindDouble(7, ProductEntity.billAmount)
          bindDouble(8, ProductEntity.tax)
          bindLong(9, ProductEntity.vat)
          bindString(10, ProductEntity.pic)
          bindLong(11, ProductEntity.stock)
          bindString(12, ProductEntity.ingredients)
          bindString(13, ProductEntity.preparationTime)
          bindString(14, ProductEntity.servingSize)
          bindLong(15, ProductEntity.dailyCapacity)
          bindLong(16, ProductEntity.discountType)
          bindDouble(17, ProductEntity.discountedAmount)
          bindLong(18, ProductEntity.brandId)
          bindLong(19, ProductEntity.businessId)
          bindLong(20, ProductEntity.storeId)
          bindLong(21, ProductEntity.unitId)
          bindLong(22, ProductEntity.createdBy)
          bindString(23, ProductEntity.createdOn)
          bindLong(24, ProductEntity.modifiedBy)
          bindString(25, ProductEntity.modifiedOn)
          bindLong(26, ProductEntity.createdAt)
          bindLong(27, ProductEntity.updatedAt)
          bindLong(28, ProductEntity.synced)
        }
    notifyQueries(1_551_295_814) { emit ->
      emit("ProductEntity")
    }
    return result
  }

  private inner class GetProductByIdQuery<out T : Any>(
    public val id: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("ProductEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("ProductEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(96_821_764,
        """SELECT ProductEntity.id, ProductEntity.productId, ProductEntity.name, ProductEntity.description, ProductEntity.additionalInfo, ProductEntity.category, ProductEntity.categoryId, ProductEntity.price, ProductEntity.billAmount, ProductEntity.tax, ProductEntity.vat, ProductEntity.pic, ProductEntity.stock, ProductEntity.ingredients, ProductEntity.preparationTime, ProductEntity.servingSize, ProductEntity.dailyCapacity, ProductEntity.discountType, ProductEntity.discountedAmount, ProductEntity.brandId, ProductEntity.businessId, ProductEntity.storeId, ProductEntity.unitId, ProductEntity.createdBy, ProductEntity.createdOn, ProductEntity.modifiedBy, ProductEntity.modifiedOn, ProductEntity.createdAt, ProductEntity.updatedAt, ProductEntity.synced FROM ProductEntity WHERE id = ?""",
        mapper, 1) {
      bindLong(0, id)
    }

    override fun toString(): String = "Product.sq:getProductById"
  }

  private inner class GetProductByProductIdQuery<out T : Any>(
    public val productId: Long?,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("ProductEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("ProductEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(null,
        """SELECT ProductEntity.id, ProductEntity.productId, ProductEntity.name, ProductEntity.description, ProductEntity.additionalInfo, ProductEntity.category, ProductEntity.categoryId, ProductEntity.price, ProductEntity.billAmount, ProductEntity.tax, ProductEntity.vat, ProductEntity.pic, ProductEntity.stock, ProductEntity.ingredients, ProductEntity.preparationTime, ProductEntity.servingSize, ProductEntity.dailyCapacity, ProductEntity.discountType, ProductEntity.discountedAmount, ProductEntity.brandId, ProductEntity.businessId, ProductEntity.storeId, ProductEntity.unitId, ProductEntity.createdBy, ProductEntity.createdOn, ProductEntity.modifiedBy, ProductEntity.modifiedOn, ProductEntity.createdAt, ProductEntity.updatedAt, ProductEntity.synced FROM ProductEntity WHERE productId ${ if (productId == null) "IS" else "=" } ?""",
        mapper, 1) {
      bindLong(0, productId)
    }

    override fun toString(): String = "Product.sq:getProductByProductId"
  }

  private inner class GetProductsByStoreQuery<out T : Any>(
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("ProductEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("ProductEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-699_460_631,
        """SELECT ProductEntity.id, ProductEntity.productId, ProductEntity.name, ProductEntity.description, ProductEntity.additionalInfo, ProductEntity.category, ProductEntity.categoryId, ProductEntity.price, ProductEntity.billAmount, ProductEntity.tax, ProductEntity.vat, ProductEntity.pic, ProductEntity.stock, ProductEntity.ingredients, ProductEntity.preparationTime, ProductEntity.servingSize, ProductEntity.dailyCapacity, ProductEntity.discountType, ProductEntity.discountedAmount, ProductEntity.brandId, ProductEntity.businessId, ProductEntity.storeId, ProductEntity.unitId, ProductEntity.createdBy, ProductEntity.createdOn, ProductEntity.modifiedBy, ProductEntity.modifiedOn, ProductEntity.createdAt, ProductEntity.updatedAt, ProductEntity.synced FROM ProductEntity WHERE storeId = ? ORDER BY category ASC, name ASC""",
        mapper, 1) {
      bindLong(0, storeId)
    }

    override fun toString(): String = "Product.sq:getProductsByStore"
  }

  private inner class GetProductsByCategoryQuery<out T : Any>(
    public val categoryId: Long?,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("ProductEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("ProductEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(null,
        """SELECT ProductEntity.id, ProductEntity.productId, ProductEntity.name, ProductEntity.description, ProductEntity.additionalInfo, ProductEntity.category, ProductEntity.categoryId, ProductEntity.price, ProductEntity.billAmount, ProductEntity.tax, ProductEntity.vat, ProductEntity.pic, ProductEntity.stock, ProductEntity.ingredients, ProductEntity.preparationTime, ProductEntity.servingSize, ProductEntity.dailyCapacity, ProductEntity.discountType, ProductEntity.discountedAmount, ProductEntity.brandId, ProductEntity.businessId, ProductEntity.storeId, ProductEntity.unitId, ProductEntity.createdBy, ProductEntity.createdOn, ProductEntity.modifiedBy, ProductEntity.modifiedOn, ProductEntity.createdAt, ProductEntity.updatedAt, ProductEntity.synced FROM ProductEntity WHERE categoryId ${ if (categoryId == null) "IS" else "=" } ? ORDER BY name ASC""",
        mapper, 1) {
      bindLong(0, categoryId)
    }

    override fun toString(): String = "Product.sq:getProductsByCategory"
  }

  private inner class GetProductsByCategoryAndStoreQuery<out T : Any>(
    public val categoryId: Long?,
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("ProductEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("ProductEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(null,
        """SELECT ProductEntity.id, ProductEntity.productId, ProductEntity.name, ProductEntity.description, ProductEntity.additionalInfo, ProductEntity.category, ProductEntity.categoryId, ProductEntity.price, ProductEntity.billAmount, ProductEntity.tax, ProductEntity.vat, ProductEntity.pic, ProductEntity.stock, ProductEntity.ingredients, ProductEntity.preparationTime, ProductEntity.servingSize, ProductEntity.dailyCapacity, ProductEntity.discountType, ProductEntity.discountedAmount, ProductEntity.brandId, ProductEntity.businessId, ProductEntity.storeId, ProductEntity.unitId, ProductEntity.createdBy, ProductEntity.createdOn, ProductEntity.modifiedBy, ProductEntity.modifiedOn, ProductEntity.createdAt, ProductEntity.updatedAt, ProductEntity.synced FROM ProductEntity WHERE categoryId ${ if (categoryId == null) "IS" else "=" } ? AND storeId = ? ORDER BY name ASC""",
        mapper, 2) {
      bindLong(0, categoryId)
      bindLong(1, storeId)
    }

    override fun toString(): String = "Product.sq:getProductsByCategoryAndStore"
  }

  private inner class GetInStockProductsByStoreQuery<out T : Any>(
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("ProductEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("ProductEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(1_369_468_690,
        """SELECT ProductEntity.id, ProductEntity.productId, ProductEntity.name, ProductEntity.description, ProductEntity.additionalInfo, ProductEntity.category, ProductEntity.categoryId, ProductEntity.price, ProductEntity.billAmount, ProductEntity.tax, ProductEntity.vat, ProductEntity.pic, ProductEntity.stock, ProductEntity.ingredients, ProductEntity.preparationTime, ProductEntity.servingSize, ProductEntity.dailyCapacity, ProductEntity.discountType, ProductEntity.discountedAmount, ProductEntity.brandId, ProductEntity.businessId, ProductEntity.storeId, ProductEntity.unitId, ProductEntity.createdBy, ProductEntity.createdOn, ProductEntity.modifiedBy, ProductEntity.modifiedOn, ProductEntity.createdAt, ProductEntity.updatedAt, ProductEntity.synced FROM ProductEntity WHERE stock = 1 AND storeId = ? ORDER BY category ASC, name ASC""",
        mapper, 1) {
      bindLong(0, storeId)
    }

    override fun toString(): String = "Product.sq:getInStockProductsByStore"
  }

  private inner class GetProductsByStockStatusQuery<out T : Any>(
    public val stock: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("ProductEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("ProductEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-1_771_618_384,
        """SELECT ProductEntity.id, ProductEntity.productId, ProductEntity.name, ProductEntity.description, ProductEntity.additionalInfo, ProductEntity.category, ProductEntity.categoryId, ProductEntity.price, ProductEntity.billAmount, ProductEntity.tax, ProductEntity.vat, ProductEntity.pic, ProductEntity.stock, ProductEntity.ingredients, ProductEntity.preparationTime, ProductEntity.servingSize, ProductEntity.dailyCapacity, ProductEntity.discountType, ProductEntity.discountedAmount, ProductEntity.brandId, ProductEntity.businessId, ProductEntity.storeId, ProductEntity.unitId, ProductEntity.createdBy, ProductEntity.createdOn, ProductEntity.modifiedBy, ProductEntity.modifiedOn, ProductEntity.createdAt, ProductEntity.updatedAt, ProductEntity.synced FROM ProductEntity WHERE stock = ? ORDER BY name ASC""",
        mapper, 1) {
      bindLong(0, stock)
    }

    override fun toString(): String = "Product.sq:getProductsByStockStatus"
  }

  private inner class SearchProductsByNameQuery<out T : Any>(
    public val `value`: String,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("ProductEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("ProductEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-1_329_836_825,
        """SELECT ProductEntity.id, ProductEntity.productId, ProductEntity.name, ProductEntity.description, ProductEntity.additionalInfo, ProductEntity.category, ProductEntity.categoryId, ProductEntity.price, ProductEntity.billAmount, ProductEntity.tax, ProductEntity.vat, ProductEntity.pic, ProductEntity.stock, ProductEntity.ingredients, ProductEntity.preparationTime, ProductEntity.servingSize, ProductEntity.dailyCapacity, ProductEntity.discountType, ProductEntity.discountedAmount, ProductEntity.brandId, ProductEntity.businessId, ProductEntity.storeId, ProductEntity.unitId, ProductEntity.createdBy, ProductEntity.createdOn, ProductEntity.modifiedBy, ProductEntity.modifiedOn, ProductEntity.createdAt, ProductEntity.updatedAt, ProductEntity.synced FROM ProductEntity WHERE name LIKE '%' || ? || '%' ORDER BY name ASC""",
        mapper, 1) {
      bindString(0, value)
    }

    override fun toString(): String = "Product.sq:searchProductsByName"
  }

  private inner class SearchProductsByNameAndStoreQuery<out T : Any>(
    public val `value`: String,
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("ProductEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("ProductEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(435_615_569,
        """SELECT ProductEntity.id, ProductEntity.productId, ProductEntity.name, ProductEntity.description, ProductEntity.additionalInfo, ProductEntity.category, ProductEntity.categoryId, ProductEntity.price, ProductEntity.billAmount, ProductEntity.tax, ProductEntity.vat, ProductEntity.pic, ProductEntity.stock, ProductEntity.ingredients, ProductEntity.preparationTime, ProductEntity.servingSize, ProductEntity.dailyCapacity, ProductEntity.discountType, ProductEntity.discountedAmount, ProductEntity.brandId, ProductEntity.businessId, ProductEntity.storeId, ProductEntity.unitId, ProductEntity.createdBy, ProductEntity.createdOn, ProductEntity.modifiedBy, ProductEntity.modifiedOn, ProductEntity.createdAt, ProductEntity.updatedAt, ProductEntity.synced FROM ProductEntity WHERE name LIKE '%' || ? || '%' AND storeId = ? ORDER BY name ASC""",
        mapper, 2) {
      bindString(0, value)
      bindLong(1, storeId)
    }

    override fun toString(): String = "Product.sq:searchProductsByNameAndStore"
  }

  private inner class GetProductCategoriesByStoreQuery<out T : Any>(
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("ProductEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("ProductEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(1_110_487_068,
        """SELECT DISTINCT category, categoryId FROM ProductEntity WHERE category IS NOT NULL AND storeId = ? ORDER BY category ASC""",
        mapper, 1) {
      bindLong(0, storeId)
    }

    override fun toString(): String = "Product.sq:getProductCategoriesByStore"
  }

  private inner class GetProductCountByStoreQuery<out T : Any>(
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("ProductEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("ProductEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(2_064_226_029,
        """SELECT COUNT(*) FROM ProductEntity WHERE storeId = ?""", mapper, 1) {
      bindLong(0, storeId)
    }

    override fun toString(): String = "Product.sq:getProductCountByStore"
  }

  private inner class GetInStockProductCountByStoreQuery<out T : Any>(
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("ProductEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("ProductEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-361_286_250,
        """SELECT COUNT(*) FROM ProductEntity WHERE stock = 1 AND storeId = ?""", mapper, 1) {
      bindLong(0, storeId)
    }

    override fun toString(): String = "Product.sq:getInStockProductCountByStore"
  }

  private inner class GetProductsPaginatedQuery<out T : Any>(
    public val storeId: Long,
    public val `value`: Long,
    public val value_: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("ProductEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("ProductEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-325_230_548, """
    |SELECT ProductEntity.id, ProductEntity.productId, ProductEntity.name, ProductEntity.description, ProductEntity.additionalInfo, ProductEntity.category, ProductEntity.categoryId, ProductEntity.price, ProductEntity.billAmount, ProductEntity.tax, ProductEntity.vat, ProductEntity.pic, ProductEntity.stock, ProductEntity.ingredients, ProductEntity.preparationTime, ProductEntity.servingSize, ProductEntity.dailyCapacity, ProductEntity.discountType, ProductEntity.discountedAmount, ProductEntity.brandId, ProductEntity.businessId, ProductEntity.storeId, ProductEntity.unitId, ProductEntity.createdBy, ProductEntity.createdOn, ProductEntity.modifiedBy, ProductEntity.modifiedOn, ProductEntity.createdAt, ProductEntity.updatedAt, ProductEntity.synced FROM ProductEntity 
    |WHERE storeId = ?
    |ORDER BY category ASC, name ASC 
    |LIMIT ? OFFSET ?
    """.trimMargin(), mapper, 3) {
      bindLong(0, storeId)
      bindLong(1, value)
      bindLong(2, value_)
    }

    override fun toString(): String = "Product.sq:getProductsPaginated"
  }
}
