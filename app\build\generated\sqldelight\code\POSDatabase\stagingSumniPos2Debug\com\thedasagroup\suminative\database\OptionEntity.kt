package com.thedasagroup.suminative.database

import kotlin.Double
import kotlin.Long
import kotlin.String

public data class OptionEntity(
  public val id: Long,
  public val optionId: Long?,
  public val optionSetId: Long,
  public val productId: Long,
  public val name: String?,
  public val price: Double,
  public val displayOrder: Long?,
  public val status: Long?,
  public val quantity: Long?,
  public val initialQuantity: Long?,
  public val storeId: Long,
  public val createdAt: Long,
  public val updatedAt: Long,
  public val synced: Long,
)
