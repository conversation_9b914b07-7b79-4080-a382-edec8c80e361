package com.thedasagroup.suminative.ui.reservations;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = AreaTableSelectionActivity.class
)
@GeneratedEntryPoint
@InstallIn(ActivityComponent.class)
public interface AreaTableSelectionActivity_GeneratedInjector {
  void injectAreaTableSelectionActivity(AreaTableSelectionActivity areaTableSelectionActivity);
}
