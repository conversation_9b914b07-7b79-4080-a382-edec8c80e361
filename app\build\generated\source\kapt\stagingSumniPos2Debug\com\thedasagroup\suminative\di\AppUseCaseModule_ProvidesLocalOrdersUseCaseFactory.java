package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.database.LocalOrderRepository;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.domain.orders.GetLocalOrdersUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesLocalOrdersUseCaseFactory implements Factory<GetLocalOrdersUseCase> {
  private final Provider<LocalOrderRepository> ordersRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  public AppUseCaseModule_ProvidesLocalOrdersUseCaseFactory(
      Provider<LocalOrderRepository> ordersRepositoryProvider, Provider<Prefs> prefsProvider) {
    this.ordersRepositoryProvider = ordersRepositoryProvider;
    this.prefsProvider = prefsProvider;
  }

  @Override
  public GetLocalOrdersUseCase get() {
    return providesLocalOrdersUseCase(ordersRepositoryProvider.get(), prefsProvider.get());
  }

  public static AppUseCaseModule_ProvidesLocalOrdersUseCaseFactory create(
      Provider<LocalOrderRepository> ordersRepositoryProvider, Provider<Prefs> prefsProvider) {
    return new AppUseCaseModule_ProvidesLocalOrdersUseCaseFactory(ordersRepositoryProvider, prefsProvider);
  }

  public static GetLocalOrdersUseCase providesLocalOrdersUseCase(
      LocalOrderRepository ordersRepository, Prefs prefs) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesLocalOrdersUseCase(ordersRepository, prefs));
  }
}
