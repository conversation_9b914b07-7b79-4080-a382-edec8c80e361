package com.thedasagroup.suminative.ui.reservations;

import dagger.internal.DaggerGenerated;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ReservationsViewModel_Factory {
  private final Provider<GetActiveReservationsUseCase> getActiveReservationsUseCaseProvider;

  private final Provider<GetAllReservationsUseCase> getAllReservationsUseCaseProvider;

  private final Provider<CreateReservationUseCase> createReservationUseCaseProvider;

  private final Provider<EditReservationUseCase> editReservationUseCaseProvider;

  private final Provider<CancelReservationUseCase> cancelReservationUseCaseProvider;

  private final Provider<GetReservationAreasUseCase> getReservationAreasUseCaseProvider;

  private final Provider<GetReservationTablesUseCase> getReservationTablesUseCaseProvider;

  public ReservationsViewModel_Factory(
      Provider<GetActiveReservationsUseCase> getActiveReservationsUseCaseProvider,
      Provider<GetAllReservationsUseCase> getAllReservationsUseCaseProvider,
      Provider<CreateReservationUseCase> createReservationUseCaseProvider,
      Provider<EditReservationUseCase> editReservationUseCaseProvider,
      Provider<CancelReservationUseCase> cancelReservationUseCaseProvider,
      Provider<GetReservationAreasUseCase> getReservationAreasUseCaseProvider,
      Provider<GetReservationTablesUseCase> getReservationTablesUseCaseProvider) {
    this.getActiveReservationsUseCaseProvider = getActiveReservationsUseCaseProvider;
    this.getAllReservationsUseCaseProvider = getAllReservationsUseCaseProvider;
    this.createReservationUseCaseProvider = createReservationUseCaseProvider;
    this.editReservationUseCaseProvider = editReservationUseCaseProvider;
    this.cancelReservationUseCaseProvider = cancelReservationUseCaseProvider;
    this.getReservationAreasUseCaseProvider = getReservationAreasUseCaseProvider;
    this.getReservationTablesUseCaseProvider = getReservationTablesUseCaseProvider;
  }

  public ReservationsViewModel get(ReservationsState state) {
    return newInstance(state, getActiveReservationsUseCaseProvider.get(), getAllReservationsUseCaseProvider.get(), createReservationUseCaseProvider.get(), editReservationUseCaseProvider.get(), cancelReservationUseCaseProvider.get(), getReservationAreasUseCaseProvider.get(), getReservationTablesUseCaseProvider.get());
  }

  public static ReservationsViewModel_Factory create(
      Provider<GetActiveReservationsUseCase> getActiveReservationsUseCaseProvider,
      Provider<GetAllReservationsUseCase> getAllReservationsUseCaseProvider,
      Provider<CreateReservationUseCase> createReservationUseCaseProvider,
      Provider<EditReservationUseCase> editReservationUseCaseProvider,
      Provider<CancelReservationUseCase> cancelReservationUseCaseProvider,
      Provider<GetReservationAreasUseCase> getReservationAreasUseCaseProvider,
      Provider<GetReservationTablesUseCase> getReservationTablesUseCaseProvider) {
    return new ReservationsViewModel_Factory(getActiveReservationsUseCaseProvider, getAllReservationsUseCaseProvider, createReservationUseCaseProvider, editReservationUseCaseProvider, cancelReservationUseCaseProvider, getReservationAreasUseCaseProvider, getReservationTablesUseCaseProvider);
  }

  public static ReservationsViewModel newInstance(ReservationsState state,
      GetActiveReservationsUseCase getActiveReservationsUseCase,
      GetAllReservationsUseCase getAllReservationsUseCase,
      CreateReservationUseCase createReservationUseCase,
      EditReservationUseCase editReservationUseCase,
      CancelReservationUseCase cancelReservationUseCase,
      GetReservationAreasUseCase getReservationAreasUseCase,
      GetReservationTablesUseCase getReservationTablesUseCase) {
    return new ReservationsViewModel(state, getActiveReservationsUseCase, getAllReservationsUseCase, createReservationUseCase, editReservationUseCase, cancelReservationUseCase, getReservationAreasUseCase, getReservationTablesUseCase);
  }
}
