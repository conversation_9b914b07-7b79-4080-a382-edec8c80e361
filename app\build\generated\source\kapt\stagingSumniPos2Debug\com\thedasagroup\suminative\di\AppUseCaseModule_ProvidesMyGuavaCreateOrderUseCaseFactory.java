package com.thedasagroup.suminative.di;

import com.instacart.truetime.time.TrueTimeImpl;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.MyGuavaRepository;
import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateOrderUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesMyGuavaCreateOrderUseCaseFactory implements Factory<MyGuavaCreateOrderUseCase> {
  private final Provider<MyGuavaRepository> myGuavaRepositoryProvider;

  private final Provider<TrueTimeImpl> trueTimeImplProvider;

  private final Provider<Prefs> prefsProvider;

  public AppUseCaseModule_ProvidesMyGuavaCreateOrderUseCaseFactory(
      Provider<MyGuavaRepository> myGuavaRepositoryProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider, Provider<Prefs> prefsProvider) {
    this.myGuavaRepositoryProvider = myGuavaRepositoryProvider;
    this.trueTimeImplProvider = trueTimeImplProvider;
    this.prefsProvider = prefsProvider;
  }

  @Override
  public MyGuavaCreateOrderUseCase get() {
    return providesMyGuavaCreateOrderUseCase(myGuavaRepositoryProvider.get(), trueTimeImplProvider.get(), prefsProvider.get());
  }

  public static AppUseCaseModule_ProvidesMyGuavaCreateOrderUseCaseFactory create(
      Provider<MyGuavaRepository> myGuavaRepositoryProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider, Provider<Prefs> prefsProvider) {
    return new AppUseCaseModule_ProvidesMyGuavaCreateOrderUseCaseFactory(myGuavaRepositoryProvider, trueTimeImplProvider, prefsProvider);
  }

  public static MyGuavaCreateOrderUseCase providesMyGuavaCreateOrderUseCase(
      MyGuavaRepository myGuavaRepository, TrueTimeImpl trueTimeImpl, Prefs prefs) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesMyGuavaCreateOrderUseCase(myGuavaRepository, trueTimeImpl, prefs));
  }
}
