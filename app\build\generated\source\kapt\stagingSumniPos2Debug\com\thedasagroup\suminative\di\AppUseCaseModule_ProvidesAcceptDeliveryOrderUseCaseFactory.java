package com.thedasagroup.suminative.di;

import com.instacart.truetime.time.TrueTimeImpl;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.OrdersRepository;
import com.thedasagroup.suminative.ui.orders.ChangeStatusAndOrdersUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesAcceptDeliveryOrderUseCaseFactory implements Factory<ChangeStatusAndOrdersUseCase> {
  private final Provider<Prefs> prefsProvider;

  private final Provider<OrdersRepository> ordersRepositoryProvider;

  private final Provider<TrueTimeImpl> trueTimeImplProvider;

  public AppUseCaseModule_ProvidesAcceptDeliveryOrderUseCaseFactory(Provider<Prefs> prefsProvider,
      Provider<OrdersRepository> ordersRepositoryProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider) {
    this.prefsProvider = prefsProvider;
    this.ordersRepositoryProvider = ordersRepositoryProvider;
    this.trueTimeImplProvider = trueTimeImplProvider;
  }

  @Override
  public ChangeStatusAndOrdersUseCase get() {
    return providesAcceptDeliveryOrderUseCase(prefsProvider.get(), ordersRepositoryProvider.get(), trueTimeImplProvider.get());
  }

  public static AppUseCaseModule_ProvidesAcceptDeliveryOrderUseCaseFactory create(
      Provider<Prefs> prefsProvider, Provider<OrdersRepository> ordersRepositoryProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider) {
    return new AppUseCaseModule_ProvidesAcceptDeliveryOrderUseCaseFactory(prefsProvider, ordersRepositoryProvider, trueTimeImplProvider);
  }

  public static ChangeStatusAndOrdersUseCase providesAcceptDeliveryOrderUseCase(Prefs prefs,
      OrdersRepository ordersRepository, TrueTimeImpl trueTimeImpl) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesAcceptDeliveryOrderUseCase(prefs, ordersRepository, trueTimeImpl));
  }
}
