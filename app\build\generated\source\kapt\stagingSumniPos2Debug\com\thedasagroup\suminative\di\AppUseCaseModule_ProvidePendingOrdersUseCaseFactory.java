package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.OrdersRepository;
import com.thedasagroup.suminative.ui.orders.GetPendingOrdersPagedUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidePendingOrdersUseCaseFactory implements Factory<GetPendingOrdersPagedUseCase> {
  private final Provider<OrdersRepository> ordersRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  public AppUseCaseModule_ProvidePendingOrdersUseCaseFactory(
      Provider<OrdersRepository> ordersRepositoryProvider, Provider<Prefs> prefsProvider) {
    this.ordersRepositoryProvider = ordersRepositoryProvider;
    this.prefsProvider = prefsProvider;
  }

  @Override
  public GetPendingOrdersPagedUseCase get() {
    return providePendingOrdersUseCase(ordersRepositoryProvider.get(), prefsProvider.get());
  }

  public static AppUseCaseModule_ProvidePendingOrdersUseCaseFactory create(
      Provider<OrdersRepository> ordersRepositoryProvider, Provider<Prefs> prefsProvider) {
    return new AppUseCaseModule_ProvidePendingOrdersUseCaseFactory(ordersRepositoryProvider, prefsProvider);
  }

  public static GetPendingOrdersPagedUseCase providePendingOrdersUseCase(
      OrdersRepository ordersRepository, Prefs prefs) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providePendingOrdersUseCase(ordersRepository, prefs));
  }
}
