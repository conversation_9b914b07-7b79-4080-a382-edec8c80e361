package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.RewardsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesRewardsRepositoryFactory implements Factory<RewardsRepository> {
  @Override
  public RewardsRepository get() {
    return providesRewardsRepository();
  }

  public static RepoModule_ProvidesRewardsRepositoryFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static RewardsRepository providesRewardsRepository() {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesRewardsRepository());
  }

  private static final class InstanceHolder {
    static final RepoModule_ProvidesRewardsRepositoryFactory INSTANCE = new RepoModule_ProvidesRewardsRepositoryFactory();
  }
}
