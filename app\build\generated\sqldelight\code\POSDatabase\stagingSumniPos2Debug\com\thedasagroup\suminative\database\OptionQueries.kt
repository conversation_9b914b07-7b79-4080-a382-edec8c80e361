package com.thedasagroup.suminative.database

import app.cash.sqldelight.Query
import app.cash.sqldelight.TransacterImpl
import app.cash.sqldelight.db.QueryResult
import app.cash.sqldelight.db.SqlCursor
import app.cash.sqldelight.db.SqlDriver
import kotlin.Any
import kotlin.Double
import kotlin.Long
import kotlin.String

public class OptionQueries(
  driver: SqlDriver,
) : TransacterImpl(driver) {
  public fun <T : Any> getOptionsByOptionSet(optionSetId: Long, mapper: (
    id: Long,
    optionId: Long?,
    optionSetId: Long,
    productId: Long,
    name: String?,
    price: Double,
    displayOrder: Long?,
    status: Long?,
    quantity: Long?,
    initialQuantity: Long?,
    storeId: Long,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = GetOptionsByOptionSetQuery(optionSetId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getLong(2)!!,
      cursor.getLong(3)!!,
      cursor.getString(4),
      cursor.getDouble(5)!!,
      cursor.getLong(6),
      cursor.getLong(7),
      cursor.getLong(8),
      cursor.getLong(9),
      cursor.getLong(10)!!,
      cursor.getLong(11)!!,
      cursor.getLong(12)!!,
      cursor.getLong(13)!!
    )
  }

  public fun getOptionsByOptionSet(optionSetId: Long): Query<OptionEntity> =
      getOptionsByOptionSet(optionSetId) { id, optionId, optionSetId_, productId, name, price,
      displayOrder, status, quantity, initialQuantity, storeId, createdAt, updatedAt, synced ->
    OptionEntity(
      id,
      optionId,
      optionSetId_,
      productId,
      name,
      price,
      displayOrder,
      status,
      quantity,
      initialQuantity,
      storeId,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> getOptionsByProduct(productId: Long, mapper: (
    id: Long,
    optionId: Long?,
    optionSetId: Long,
    productId: Long,
    name: String?,
    price: Double,
    displayOrder: Long?,
    status: Long?,
    quantity: Long?,
    initialQuantity: Long?,
    storeId: Long,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = GetOptionsByProductQuery(productId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getLong(2)!!,
      cursor.getLong(3)!!,
      cursor.getString(4),
      cursor.getDouble(5)!!,
      cursor.getLong(6),
      cursor.getLong(7),
      cursor.getLong(8),
      cursor.getLong(9),
      cursor.getLong(10)!!,
      cursor.getLong(11)!!,
      cursor.getLong(12)!!,
      cursor.getLong(13)!!
    )
  }

  public fun getOptionsByProduct(productId: Long): Query<OptionEntity> =
      getOptionsByProduct(productId) { id, optionId, optionSetId, productId_, name, price,
      displayOrder, status, quantity, initialQuantity, storeId, createdAt, updatedAt, synced ->
    OptionEntity(
      id,
      optionId,
      optionSetId,
      productId_,
      name,
      price,
      displayOrder,
      status,
      quantity,
      initialQuantity,
      storeId,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> getOptionsByStore(storeId: Long, mapper: (
    id: Long,
    optionId: Long?,
    optionSetId: Long,
    productId: Long,
    name: String?,
    price: Double,
    displayOrder: Long?,
    status: Long?,
    quantity: Long?,
    initialQuantity: Long?,
    storeId: Long,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = GetOptionsByStoreQuery(storeId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getLong(2)!!,
      cursor.getLong(3)!!,
      cursor.getString(4),
      cursor.getDouble(5)!!,
      cursor.getLong(6),
      cursor.getLong(7),
      cursor.getLong(8),
      cursor.getLong(9),
      cursor.getLong(10)!!,
      cursor.getLong(11)!!,
      cursor.getLong(12)!!,
      cursor.getLong(13)!!
    )
  }

  public fun getOptionsByStore(storeId: Long): Query<OptionEntity> = getOptionsByStore(storeId) {
      id, optionId, optionSetId, productId, name, price, displayOrder, status, quantity,
      initialQuantity, storeId_, createdAt, updatedAt, synced ->
    OptionEntity(
      id,
      optionId,
      optionSetId,
      productId,
      name,
      price,
      displayOrder,
      status,
      quantity,
      initialQuantity,
      storeId_,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> getOptionById(optionId: Long?, mapper: (
    id: Long,
    optionId: Long?,
    optionSetId: Long,
    productId: Long,
    name: String?,
    price: Double,
    displayOrder: Long?,
    status: Long?,
    quantity: Long?,
    initialQuantity: Long?,
    storeId: Long,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = GetOptionByIdQuery(optionId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getLong(2)!!,
      cursor.getLong(3)!!,
      cursor.getString(4),
      cursor.getDouble(5)!!,
      cursor.getLong(6),
      cursor.getLong(7),
      cursor.getLong(8),
      cursor.getLong(9),
      cursor.getLong(10)!!,
      cursor.getLong(11)!!,
      cursor.getLong(12)!!,
      cursor.getLong(13)!!
    )
  }

  public fun getOptionById(optionId: Long?): Query<OptionEntity> = getOptionById(optionId) { id,
      optionId_, optionSetId, productId, name, price, displayOrder, status, quantity,
      initialQuantity, storeId, createdAt, updatedAt, synced ->
    OptionEntity(
      id,
      optionId_,
      optionSetId,
      productId,
      name,
      price,
      displayOrder,
      status,
      quantity,
      initialQuantity,
      storeId,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun countOptionsByOptionSet(optionSetId: Long): Query<Long> =
      CountOptionsByOptionSetQuery(optionSetId) { cursor ->
    cursor.getLong(0)!!
  }

  public fun countOptionsByProduct(productId: Long): Query<Long> =
      CountOptionsByProductQuery(productId) { cursor ->
    cursor.getLong(0)!!
  }

  public fun countTotalOptions(): Query<Long> = Query(195_810_466, arrayOf("OptionEntity"), driver,
      "Option.sq", "countTotalOptions", "SELECT COUNT(*) FROM OptionEntity") { cursor ->
    cursor.getLong(0)!!
  }

  /**
   * @return The number of rows updated.
   */
  public fun insertOption(
    optionId: Long?,
    optionSetId: Long,
    productId: Long,
    name: String?,
    price: Double,
    displayOrder: Long?,
    status: Long?,
    quantity: Long?,
    initialQuantity: Long?,
    storeId: Long,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ): QueryResult<Long> {
    val result = driver.execute(234_858_933, """
        |INSERT INTO OptionEntity (
        |    optionId, optionSetId, productId, name, price, displayOrder,
        |    status, quantity, initialQuantity, storeId, createdAt, updatedAt, synced
        |) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """.trimMargin(), 13) {
          bindLong(0, optionId)
          bindLong(1, optionSetId)
          bindLong(2, productId)
          bindString(3, name)
          bindDouble(4, price)
          bindLong(5, displayOrder)
          bindLong(6, status)
          bindLong(7, quantity)
          bindLong(8, initialQuantity)
          bindLong(9, storeId)
          bindLong(10, createdAt)
          bindLong(11, updatedAt)
          bindLong(12, synced)
        }
    notifyQueries(234_858_933) { emit ->
      emit("OptionEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun insertOrReplaceOption(
    optionId: Long?,
    optionSetId: Long,
    productId: Long,
    name: String?,
    price: Double,
    displayOrder: Long?,
    status: Long?,
    quantity: Long?,
    initialQuantity: Long?,
    storeId: Long,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ): QueryResult<Long> {
    val result = driver.execute(-563_961_370, """
        |INSERT OR REPLACE INTO OptionEntity (
        |    optionId, optionSetId, productId, name, price, displayOrder,
        |    status, quantity, initialQuantity, storeId, createdAt, updatedAt, synced
        |) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """.trimMargin(), 13) {
          bindLong(0, optionId)
          bindLong(1, optionSetId)
          bindLong(2, productId)
          bindString(3, name)
          bindDouble(4, price)
          bindLong(5, displayOrder)
          bindLong(6, status)
          bindLong(7, quantity)
          bindLong(8, initialQuantity)
          bindLong(9, storeId)
          bindLong(10, createdAt)
          bindLong(11, updatedAt)
          bindLong(12, synced)
        }
    notifyQueries(-563_961_370) { emit ->
      emit("OptionEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun updateOption(
    name: String?,
    price: Double,
    displayOrder: Long?,
    status: Long?,
    quantity: Long?,
    initialQuantity: Long?,
    updatedAt: Long,
    synced: Long,
    optionId: Long?,
  ): QueryResult<Long> {
    val result = driver.execute(null, """
        |UPDATE OptionEntity 
        |SET name = ?, price = ?, displayOrder = ?, status = ?,
        |    quantity = ?, initialQuantity = ?, updatedAt = ?, synced = ?
        |WHERE optionId ${ if (optionId == null) "IS" else "=" } ?
        """.trimMargin(), 9) {
          bindString(0, name)
          bindDouble(1, price)
          bindLong(2, displayOrder)
          bindLong(3, status)
          bindLong(4, quantity)
          bindLong(5, initialQuantity)
          bindLong(6, updatedAt)
          bindLong(7, synced)
          bindLong(8, optionId)
        }
    notifyQueries(-1_459_465_275) { emit ->
      emit("OptionEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun deleteOptionsByOptionSet(optionSetId: Long): QueryResult<Long> {
    val result = driver.execute(-365_801_654, """DELETE FROM OptionEntity WHERE optionSetId = ?""",
        1) {
          bindLong(0, optionSetId)
        }
    notifyQueries(-365_801_654) { emit ->
      emit("OptionEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun deleteOptionsByProduct(productId: Long): QueryResult<Long> {
    val result = driver.execute(-477_137_972, """DELETE FROM OptionEntity WHERE productId = ?""", 1)
        {
          bindLong(0, productId)
        }
    notifyQueries(-477_137_972) { emit ->
      emit("OptionEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun deleteAllOptions(): QueryResult<Long> {
    val result = driver.execute(-1_659_929_745, """DELETE FROM OptionEntity""", 0)
    notifyQueries(-1_659_929_745) { emit ->
      emit("OptionEntity")
    }
    return result
  }

  private inner class GetOptionsByOptionSetQuery<out T : Any>(
    public val optionSetId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OptionEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OptionEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-1_424_687_033,
        """SELECT OptionEntity.id, OptionEntity.optionId, OptionEntity.optionSetId, OptionEntity.productId, OptionEntity.name, OptionEntity.price, OptionEntity.displayOrder, OptionEntity.status, OptionEntity.quantity, OptionEntity.initialQuantity, OptionEntity.storeId, OptionEntity.createdAt, OptionEntity.updatedAt, OptionEntity.synced FROM OptionEntity WHERE optionSetId = ? ORDER BY displayOrder ASC""",
        mapper, 1) {
      bindLong(0, optionSetId)
    }

    override fun toString(): String = "Option.sq:getOptionsByOptionSet"
  }

  private inner class GetOptionsByProductQuery<out T : Any>(
    public val productId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OptionEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OptionEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-898_351_095,
        """SELECT OptionEntity.id, OptionEntity.optionId, OptionEntity.optionSetId, OptionEntity.productId, OptionEntity.name, OptionEntity.price, OptionEntity.displayOrder, OptionEntity.status, OptionEntity.quantity, OptionEntity.initialQuantity, OptionEntity.storeId, OptionEntity.createdAt, OptionEntity.updatedAt, OptionEntity.synced FROM OptionEntity WHERE productId = ? ORDER BY displayOrder ASC""",
        mapper, 1) {
      bindLong(0, productId)
    }

    override fun toString(): String = "Option.sq:getOptionsByProduct"
  }

  private inner class GetOptionsByStoreQuery<out T : Any>(
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OptionEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OptionEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-2_134_414_725,
        """SELECT OptionEntity.id, OptionEntity.optionId, OptionEntity.optionSetId, OptionEntity.productId, OptionEntity.name, OptionEntity.price, OptionEntity.displayOrder, OptionEntity.status, OptionEntity.quantity, OptionEntity.initialQuantity, OptionEntity.storeId, OptionEntity.createdAt, OptionEntity.updatedAt, OptionEntity.synced FROM OptionEntity WHERE storeId = ? ORDER BY displayOrder ASC""",
        mapper, 1) {
      bindLong(0, storeId)
    }

    override fun toString(): String = "Option.sq:getOptionsByStore"
  }

  private inner class GetOptionByIdQuery<out T : Any>(
    public val optionId: Long?,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OptionEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OptionEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(null,
        """SELECT OptionEntity.id, OptionEntity.optionId, OptionEntity.optionSetId, OptionEntity.productId, OptionEntity.name, OptionEntity.price, OptionEntity.displayOrder, OptionEntity.status, OptionEntity.quantity, OptionEntity.initialQuantity, OptionEntity.storeId, OptionEntity.createdAt, OptionEntity.updatedAt, OptionEntity.synced FROM OptionEntity WHERE optionId ${ if (optionId == null) "IS" else "=" } ?""",
        mapper, 1) {
      bindLong(0, optionId)
    }

    override fun toString(): String = "Option.sq:getOptionById"
  }

  private inner class CountOptionsByOptionSetQuery<out T : Any>(
    public val optionSetId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OptionEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OptionEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-823_567_488,
        """SELECT COUNT(*) FROM OptionEntity WHERE optionSetId = ?""", mapper, 1) {
      bindLong(0, optionSetId)
    }

    override fun toString(): String = "Option.sq:countOptionsByOptionSet"
  }

  private inner class CountOptionsByProductQuery<out T : Any>(
    public val productId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OptionEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OptionEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(2_078_807_426,
        """SELECT COUNT(*) FROM OptionEntity WHERE productId = ?""", mapper, 1) {
      bindLong(0, productId)
    }

    override fun toString(): String = "Option.sq:countOptionsByProduct"
  }
}
