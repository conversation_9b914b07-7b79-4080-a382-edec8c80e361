package com.thedasagroup.suminative.ui.products;

import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ProductsScreenViewModel_Factory_Impl implements ProductsScreenViewModel.Factory {
  private final ProductsScreenViewModel_Factory delegateFactory;

  ProductsScreenViewModel_Factory_Impl(ProductsScreenViewModel_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public ProductsScreenViewModel create(ProductsScreenState state) {
    return delegateFactory.get(state);
  }

  public static Provider<ProductsScreenViewModel.Factory> create(
      ProductsScreenViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new ProductsScreenViewModel_Factory_Impl(delegateFactory));
  }

  public static dagger.internal.Provider<ProductsScreenViewModel.Factory> createFactoryProvider(
      ProductsScreenViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new ProductsScreenViewModel_Factory_Impl(delegateFactory));
  }
}
