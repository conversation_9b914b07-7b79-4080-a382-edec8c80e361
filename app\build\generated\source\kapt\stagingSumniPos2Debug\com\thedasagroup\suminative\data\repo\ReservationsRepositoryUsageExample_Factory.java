package com.thedasagroup.suminative.data.repo;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ReservationsRepositoryUsageExample_Factory implements Factory<ReservationsRepositoryUsageExample> {
  private final Provider<ReservationsRepository> reservationsRepositoryProvider;

  public ReservationsRepositoryUsageExample_Factory(
      Provider<ReservationsRepository> reservationsRepositoryProvider) {
    this.reservationsRepositoryProvider = reservationsRepositoryProvider;
  }

  @Override
  public ReservationsRepositoryUsageExample get() {
    return newInstance(reservationsRepositoryProvider.get());
  }

  public static ReservationsRepositoryUsageExample_Factory create(
      Provider<ReservationsRepository> reservationsRepositoryProvider) {
    return new ReservationsRepositoryUsageExample_Factory(reservationsRepositoryProvider);
  }

  public static ReservationsRepositoryUsageExample newInstance(
      ReservationsRepository reservationsRepository) {
    return new ReservationsRepositoryUsageExample(reservationsRepository);
  }
}
