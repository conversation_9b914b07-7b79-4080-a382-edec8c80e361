package com.thedasagroup.suminative.di;

import android.content.Context;
import com.thedasagroup.suminative.work.OrderSyncManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesOrderSyncManagerFactory implements Factory<OrderSyncManager> {
  private final Provider<Context> contextProvider;

  public RepoModule_ProvidesOrderSyncManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public OrderSyncManager get() {
    return providesOrderSyncManager(contextProvider.get());
  }

  public static RepoModule_ProvidesOrderSyncManagerFactory create(
      Provider<Context> contextProvider) {
    return new RepoModule_ProvidesOrderSyncManagerFactory(contextProvider);
  }

  public static OrderSyncManager providesOrderSyncManager(Context context) {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesOrderSyncManager(context));
  }
}
