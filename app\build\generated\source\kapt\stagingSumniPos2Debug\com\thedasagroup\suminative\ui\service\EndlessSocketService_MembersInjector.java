package com.thedasagroup.suminative.ui.service;

import com.instacart.truetime.time.TrueTimeImpl;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.OrdersRepository;
import com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase;
import com.thedasagroup.suminative.ui.utils.SoundPoolPlayer;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class EndlessSocketService_MembersInjector implements MembersInjector<EndlessSocketService> {
  private final Provider<Prefs> prefsProvider;

  private final Provider<OrdersRepository> orderRepositoryProvider;

  private final Provider<GetStoreSettingsUseCase> getStoreSettingsProvider;

  private final Provider<TrueTimeImpl> trueTimeImplProvider;

  private final Provider<SoundPoolPlayer> soundPoolPlayerProvider;

  public EndlessSocketService_MembersInjector(Provider<Prefs> prefsProvider,
      Provider<OrdersRepository> orderRepositoryProvider,
      Provider<GetStoreSettingsUseCase> getStoreSettingsProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider,
      Provider<SoundPoolPlayer> soundPoolPlayerProvider) {
    this.prefsProvider = prefsProvider;
    this.orderRepositoryProvider = orderRepositoryProvider;
    this.getStoreSettingsProvider = getStoreSettingsProvider;
    this.trueTimeImplProvider = trueTimeImplProvider;
    this.soundPoolPlayerProvider = soundPoolPlayerProvider;
  }

  public static MembersInjector<EndlessSocketService> create(Provider<Prefs> prefsProvider,
      Provider<OrdersRepository> orderRepositoryProvider,
      Provider<GetStoreSettingsUseCase> getStoreSettingsProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider,
      Provider<SoundPoolPlayer> soundPoolPlayerProvider) {
    return new EndlessSocketService_MembersInjector(prefsProvider, orderRepositoryProvider, getStoreSettingsProvider, trueTimeImplProvider, soundPoolPlayerProvider);
  }

  @Override
  public void injectMembers(EndlessSocketService instance) {
    injectPrefs(instance, prefsProvider.get());
    injectOrderRepository(instance, orderRepositoryProvider.get());
    injectGetStoreSettings(instance, getStoreSettingsProvider.get());
    injectTrueTimeImpl(instance, trueTimeImplProvider.get());
    injectSoundPoolPlayer(instance, soundPoolPlayerProvider.get());
  }

  @InjectedFieldSignature("com.thedasagroup.suminative.ui.service.EndlessSocketService.prefs")
  public static void injectPrefs(EndlessSocketService instance, Prefs prefs) {
    instance.prefs = prefs;
  }

  @InjectedFieldSignature("com.thedasagroup.suminative.ui.service.EndlessSocketService.orderRepository")
  public static void injectOrderRepository(EndlessSocketService instance,
      OrdersRepository orderRepository) {
    instance.orderRepository = orderRepository;
  }

  @InjectedFieldSignature("com.thedasagroup.suminative.ui.service.EndlessSocketService.getStoreSettings")
  public static void injectGetStoreSettings(EndlessSocketService instance,
      GetStoreSettingsUseCase getStoreSettings) {
    instance.getStoreSettings = getStoreSettings;
  }

  @InjectedFieldSignature("com.thedasagroup.suminative.ui.service.EndlessSocketService.trueTimeImpl")
  public static void injectTrueTimeImpl(EndlessSocketService instance, TrueTimeImpl trueTimeImpl) {
    instance.trueTimeImpl = trueTimeImpl;
  }

  @InjectedFieldSignature("com.thedasagroup.suminative.ui.service.EndlessSocketService.soundPoolPlayer")
  public static void injectSoundPoolPlayer(EndlessSocketService instance,
      SoundPoolPlayer soundPoolPlayer) {
    instance.soundPoolPlayer = soundPoolPlayer;
  }
}
