package com.thedasagroup.suminative.di;

import android.content.Context;
import android.media.AudioManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesAudioManagerFactory implements Factory<AudioManager> {
  private final Provider<Context> contextProvider;

  public RepoModule_ProvidesAudioManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public AudioManager get() {
    return providesAudioManager(contextProvider.get());
  }

  public static RepoModule_ProvidesAudioManagerFactory create(Provider<Context> contextProvider) {
    return new RepoModule_ProvidesAudioManagerFactory(contextProvider);
  }

  public static AudioManager providesAudioManager(Context context) {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesAudioManager(context));
  }
}
