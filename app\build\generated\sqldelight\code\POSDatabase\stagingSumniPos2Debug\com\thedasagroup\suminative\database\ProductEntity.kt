package com.thedasagroup.suminative.database

import kotlin.Double
import kotlin.Long
import kotlin.String

public data class ProductEntity(
  public val id: Long,
  public val productId: Long?,
  public val name: String,
  public val description: String?,
  public val additionalInfo: String?,
  public val category: String?,
  public val categoryId: Long?,
  public val price: Double,
  public val billAmount: Double?,
  public val tax: Double?,
  public val vat: Long,
  public val pic: String?,
  public val stock: Long,
  public val ingredients: String?,
  public val preparationTime: String?,
  public val servingSize: String?,
  public val dailyCapacity: Long?,
  public val discountType: Long?,
  public val discountedAmount: Double?,
  public val brandId: Long?,
  public val businessId: Long?,
  public val storeId: Long,
  public val unitId: Long?,
  public val createdBy: Long?,
  public val createdOn: String?,
  public val modifiedBy: Long?,
  public val modifiedOn: String?,
  public val createdAt: Long,
  public val updatedAt: Long,
  public val synced: Long,
)
