package com.thedasagroup.suminative.ui.refund;

import com.thedasagroup.suminative.ui.orders.GetPendingOrdersPagedUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RefundSumUpViewModel_Factory {
  private final Provider<GetPendingOrdersPagedUseCase> getPendingOrdersPagedUseCaseProvider;

  public RefundSumUpViewModel_Factory(
      Provider<GetPendingOrdersPagedUseCase> getPendingOrdersPagedUseCaseProvider) {
    this.getPendingOrdersPagedUseCaseProvider = getPendingOrdersPagedUseCaseProvider;
  }

  public RefundSumUpViewModel get(RefundSumUpState state) {
    return newInstance(state, getPendingOrdersPagedUseCaseProvider.get());
  }

  public static RefundSumUpViewModel_Factory create(
      Provider<GetPendingOrdersPagedUseCase> getPendingOrdersPagedUseCaseProvider) {
    return new RefundSumUpViewModel_Factory(getPendingOrdersPagedUseCaseProvider);
  }

  public static RefundSumUpViewModel newInstance(RefundSumUpState state,
      GetPendingOrdersPagedUseCase getPendingOrdersPagedUseCase) {
    return new RefundSumUpViewModel(state, getPendingOrdersPagedUseCase);
  }
}
