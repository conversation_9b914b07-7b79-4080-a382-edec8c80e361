package com.thedasagroup.suminative.ui.payment;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = CashPaymentActivity.class
)
@GeneratedEntryPoint
@InstallIn(ActivityComponent.class)
public interface CashPaymentActivity_GeneratedInjector {
  void injectCashPaymentActivity(CashPaymentActivity cashPaymentActivity);
}
