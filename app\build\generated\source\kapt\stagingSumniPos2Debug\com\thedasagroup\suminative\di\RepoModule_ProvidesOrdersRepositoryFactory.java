package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.OrdersRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesOrdersRepositoryFactory implements Factory<OrdersRepository> {
  @Override
  public OrdersRepository get() {
    return providesOrdersRepository();
  }

  public static RepoModule_ProvidesOrdersRepositoryFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static OrdersRepository providesOrdersRepository() {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesOrdersRepository());
  }

  private static final class InstanceHolder {
    static final RepoModule_ProvidesOrdersRepositoryFactory INSTANCE = new RepoModule_ProvidesOrdersRepositoryFactory();
  }
}
