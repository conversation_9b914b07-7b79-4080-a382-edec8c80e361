package com.thedasagroup.suminative.database.app

import app.cash.sqldelight.TransacterImpl
import app.cash.sqldelight.db.AfterVersion
import app.cash.sqldelight.db.QueryResult
import app.cash.sqldelight.db.SqlDriver
import app.cash.sqldelight.db.SqlSchema
import com.thedasagroup.suminative.database.CategoryQueries
import com.thedasagroup.suminative.database.OptionQueries
import com.thedasagroup.suminative.database.OptionSetQueries
import com.thedasagroup.suminative.database.OrderItemQueries
import com.thedasagroup.suminative.database.OrderQueries
import com.thedasagroup.suminative.database.POSDatabase
import com.thedasagroup.suminative.database.ProductQueries
import kotlin.Long
import kotlin.Unit
import kotlin.reflect.KClass

internal val KClass<POSDatabase>.schema: SqlSchema<QueryResult.Value<Unit>>
  get() = POSDatabaseImpl.Schema

internal fun KClass<POSDatabase>.newInstance(driver: SqlDriver): POSDatabase =
    POSDatabaseImpl(driver)

private class POSDatabaseImpl(
  driver: SqlDriver,
) : Transacter<PERSON>mpl(driver),
    POSDatabase {
  override val categoryQueries: CategoryQueries = CategoryQueries(driver)

  override val optionQueries: OptionQueries = OptionQueries(driver)

  override val optionSetQueries: OptionSetQueries = OptionSetQueries(driver)

  override val orderQueries: OrderQueries = OrderQueries(driver)

  override val orderItemQueries: OrderItemQueries = OrderItemQueries(driver)

  override val productQueries: ProductQueries = ProductQueries(driver)

  public object Schema : SqlSchema<QueryResult.Value<Unit>> {
    override val version: Long
      get() = 1

    override fun create(driver: SqlDriver): QueryResult.Value<Unit> {
      driver.execute(null, """
          |CREATE TABLE CategoryEntity (
          |    id INTEGER PRIMARY KEY AUTOINCREMENT,
          |    name TEXT NOT NULL,
          |    storeId INTEGER NOT NULL,
          |    sortOrder INTEGER NOT NULL DEFAULT 0,
          |    isActive INTEGER NOT NULL DEFAULT 1,
          |    createdAt INTEGER NOT NULL,
          |    updatedAt INTEGER NOT NULL,
          |    syncedAt INTEGER DEFAULT NULL,
          |    UNIQUE(name, storeId)
          |)
          """.trimMargin(), 0)
      driver.execute(null, """
          |CREATE TABLE OptionEntity (
          |    id INTEGER PRIMARY KEY AUTOINCREMENT,
          |    optionId INTEGER, -- Remote server ID
          |    optionSetId INTEGER NOT NULL, -- Link to OptionSetEntity.optionSetId
          |    productId INTEGER NOT NULL, -- Link to ProductEntity.productId
          |    name TEXT,
          |    price REAL NOT NULL DEFAULT 0.0,
          |    displayOrder INTEGER,
          |    status INTEGER,
          |    quantity INTEGER DEFAULT 0,
          |    initialQuantity INTEGER DEFAULT 1,
          |    storeId INTEGER NOT NULL,
          |    createdAt INTEGER NOT NULL, -- Local timestamp
          |    updatedAt INTEGER NOT NULL, -- Local timestamp
          |    synced INTEGER NOT NULL DEFAULT 0, -- 0 = not synced, 1 = synced
          |    
          |    FOREIGN KEY(optionSetId) REFERENCES OptionSetEntity(optionSetId),
          |    FOREIGN KEY(productId) REFERENCES ProductEntity(productId)
          |)
          """.trimMargin(), 0)
      driver.execute(null, """
          |CREATE TABLE OptionSetEntity (
          |    id INTEGER PRIMARY KEY AUTOINCREMENT,
          |    optionSetId INTEGER, -- Remote server ID
          |    productId INTEGER NOT NULL, -- Link to ProductEntity.productId
          |    name TEXT,
          |    condition INTEGER, -- 1 = select one, 2 = select any number
          |    customNumber INTEGER,
          |    displayOrder INTEGER,
          |    itemId INTEGER,
          |    status INTEGER,
          |    variantType INTEGER,
          |    storeId INTEGER NOT NULL,
          |    createdAt INTEGER NOT NULL, -- Local timestamp
          |    updatedAt INTEGER NOT NULL, -- Local timestamp
          |    synced INTEGER NOT NULL DEFAULT 0, -- 0 = not synced, 1 = synced
          |    
          |    FOREIGN KEY(productId) REFERENCES ProductEntity(productId)
          |)
          """.trimMargin(), 0)
      driver.execute(null, """
          |CREATE TABLE OrderEntity (
          |    id INTEGER PRIMARY KEY AUTOINCREMENT,
          |    orderId TEXT NOT NULL UNIQUE,
          |    customerId INTEGER,
          |    customerName TEXT,
          |    customerPhone TEXT,
          |    orderType TEXT NOT NULL, -- 'DINE_IN', 'TAKEAWAY', 'DELIVERY'
          |    status TEXT NOT NULL, -- 'PENDING', 'ACCEPTED', 'PREPARING', 'READY', 'COMPLETED', 'CANCELLED'
          |    subtotal REAL NOT NULL DEFAULT 0.0,
          |    tax REAL NOT NULL DEFAULT 0.0,
          |    discount REAL NOT NULL DEFAULT 0.0,
          |    total REAL NOT NULL DEFAULT 0.0,
          |    paymentMethod TEXT, -- 'CASH', 'CARD', 'DIGITAL'
          |    paymentStatus TEXT NOT NULL DEFAULT 'PENDING', -- 'PENDING', 'PAID', 'PARTIAL', 'REFUNDED'
          |    notes TEXT,
          |    tableNumber INTEGER,
          |    deliveryAddress TEXT,
          |    createdAt INTEGER NOT NULL, -- timestamp
          |    updatedAt INTEGER NOT NULL, -- timestamp
          |    completedAt INTEGER, -- timestamp
          |    storeId INTEGER NOT NULL,
          |    waiterId INTEGER,
          |    waiterName TEXT,
          |    synced INTEGER NOT NULL DEFAULT 0 -- 0 = not synced, 1 = synced
          |)
          """.trimMargin(), 0)
      driver.execute(null, """
          |CREATE TABLE OrderItemEntity (
          |    id INTEGER PRIMARY KEY AUTOINCREMENT,
          |    orderId TEXT NOT NULL,
          |    itemId INTEGER NOT NULL,
          |    itemName TEXT NOT NULL,
          |    itemDescription TEXT,
          |    quantity INTEGER NOT NULL DEFAULT 1,
          |    unitPrice REAL NOT NULL,
          |    totalPrice REAL NOT NULL,
          |    category TEXT,
          |    modifiers TEXT, -- JSON string for any item modifications
          |    notes TEXT,
          |    createdAt INTEGER NOT NULL,
          |    FOREIGN KEY (orderId) REFERENCES OrderEntity(orderId) ON DELETE CASCADE
          |)
          """.trimMargin(), 0)
      driver.execute(null, """
          |CREATE TABLE ProductEntity (
          |    id INTEGER PRIMARY KEY AUTOINCREMENT,
          |    productId INTEGER UNIQUE, -- Remote server ID
          |    name TEXT NOT NULL,
          |    description TEXT,
          |    additionalInfo TEXT,
          |    category TEXT,
          |    categoryId INTEGER,
          |    price REAL NOT NULL DEFAULT 0.0,
          |    billAmount REAL DEFAULT 0.0,
          |    tax REAL DEFAULT 0.0,
          |    vat INTEGER NOT NULL DEFAULT 0, -- 0 = false, 1 = true
          |    pic TEXT, -- Image URL/path
          |    stock INTEGER NOT NULL DEFAULT 0, -- 0 = off menu, 1 = in stock, 2 = sold out for today
          |    ingredients TEXT,
          |    preparationTime TEXT,
          |    servingSize TEXT,
          |    dailyCapacity INTEGER DEFAULT 0,
          |    discountType INTEGER DEFAULT 0,
          |    discountedAmount REAL DEFAULT 0.0,
          |    brandId INTEGER,
          |    businessId INTEGER,
          |    storeId INTEGER NOT NULL,
          |    unitId INTEGER,
          |    createdBy INTEGER,
          |    createdOn TEXT,
          |    modifiedBy INTEGER,
          |    modifiedOn TEXT,
          |    createdAt INTEGER NOT NULL, -- Local timestamp
          |    updatedAt INTEGER NOT NULL, -- Local timestamp
          |    synced INTEGER NOT NULL DEFAULT 0 -- 0 = not synced, 1 = synced
          |)
          """.trimMargin(), 0)
      driver.execute(null, "CREATE INDEX idx_category_store_id ON CategoryEntity(storeId)", 0)
      driver.execute(null,
          "CREATE INDEX idx_category_sort_order ON CategoryEntity(storeId, sortOrder)", 0)
      driver.execute(null, "CREATE INDEX idx_category_active ON CategoryEntity(storeId, isActive)",
          0)
      driver.execute(null, "CREATE UNIQUE INDEX idx_option_id_unique ON OptionEntity(optionId)", 0)
      driver.execute(null, "CREATE INDEX idx_option_optionset ON OptionEntity(optionSetId)", 0)
      driver.execute(null, "CREATE INDEX idx_option_product ON OptionEntity(productId)", 0)
      driver.execute(null, "CREATE INDEX idx_option_store ON OptionEntity(storeId)", 0)
      driver.execute(null, "CREATE INDEX idx_option_synced ON OptionEntity(synced)", 0)
      driver.execute(null, "CREATE INDEX idx_option_display_order ON OptionEntity(displayOrder)", 0)
      driver.execute(null,
          "CREATE UNIQUE INDEX idx_optionset_id_unique ON OptionSetEntity(optionSetId)", 0)
      driver.execute(null, "CREATE INDEX idx_optionset_product ON OptionSetEntity(productId)", 0)
      driver.execute(null, "CREATE INDEX idx_optionset_store ON OptionSetEntity(storeId)", 0)
      driver.execute(null, "CREATE INDEX idx_optionset_synced ON OptionSetEntity(synced)", 0)
      driver.execute(null,
          "CREATE INDEX idx_optionset_display_order ON OptionSetEntity(displayOrder)", 0)
      driver.execute(null, "CREATE INDEX idx_order_status ON OrderEntity(status)", 0)
      driver.execute(null, "CREATE INDEX idx_order_store ON OrderEntity(storeId)", 0)
      driver.execute(null, "CREATE INDEX idx_order_created ON OrderEntity(createdAt)", 0)
      driver.execute(null, "CREATE INDEX idx_order_type ON OrderEntity(orderType)", 0)
      driver.execute(null, "CREATE INDEX idx_order_synced ON OrderEntity(synced)", 0)
      driver.execute(null, "CREATE INDEX idx_orderitem_order ON OrderItemEntity(orderId)", 0)
      driver.execute(null, "CREATE INDEX idx_orderitem_item ON OrderItemEntity(itemId)", 0)
      driver.execute(null, "CREATE INDEX idx_product_category ON ProductEntity(categoryId)", 0)
      driver.execute(null, "CREATE INDEX idx_product_store ON ProductEntity(storeId)", 0)
      driver.execute(null, "CREATE INDEX idx_product_stock ON ProductEntity(stock)", 0)
      driver.execute(null, "CREATE INDEX idx_product_name ON ProductEntity(name)", 0)
      driver.execute(null, "CREATE INDEX idx_product_synced ON ProductEntity(synced)", 0)
      driver.execute(null, "CREATE INDEX idx_product_id ON ProductEntity(productId)", 0)
      return QueryResult.Unit
    }

    override fun migrate(
      driver: SqlDriver,
      oldVersion: Long,
      newVersion: Long,
      vararg callbacks: AfterVersion,
    ): QueryResult.Value<Unit> = QueryResult.Unit
  }
}
