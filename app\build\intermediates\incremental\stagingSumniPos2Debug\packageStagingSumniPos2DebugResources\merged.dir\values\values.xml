<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="black">#FF000000</color>
    <color name="purple_200">#FF2E7D32</color>
    <color name="purple_500">#FF2E7D32</color>
    <color name="purple_700">#FF2E7D32</color>
    <color name="teal_200">#FF2E7D32</color>
    <color name="teal_700">#FF2E7D32</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">DasaPOS</string>
    <string name="back_to_login">Back to Login</string>
    <string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string>
    <string name="dashboard_title_welcome_user">Welcome User</string>
    <string name="do_not_have_account">Don\'t have an account?</string>
    <string name="empty_string"/>
    <string name="forgot_password">Forgot Password?</string>
    <string name="gcm_defaultSenderId" translatable="false">************</string>
    <string name="google_api_key" translatable="false">AIzaSyBn8bybiTibHkeRacv0uSoJUEX19kblmBI</string>
    <string name="google_app_id" translatable="false">1:************:android:e7a8a4d6e9dd76eed6b9f4</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyBn8bybiTibHkeRacv0uSoJUEX19kblmBI</string>
    <string name="google_storage_bucket" translatable="false">dasadirect.firebasestorage.app</string>
    <string name="icon_password_hidden">Password Hidden</string>
    <string name="icon_password_visible">Password Visible</string>
    <string name="login_button_text">Login</string>
    <string name="login_email_id_or_phone_label">Email ID or Mobile Number</string>
    <string name="login_error_msg_empty_email_mobile">Please enter your email or mobile number</string>
    <string name="login_error_msg_empty_password">Please enter your password</string>
    <string name="login_heading_text">Login</string>
    <string name="login_password_label">Password</string>
    <string name="project_id" translatable="false">dasadirect</string>
    <string name="register">Register</string>
    <string name="registration_button_text">Register</string>
    <string name="registration_confirm_password_label">Confirm Password</string>
    <string name="registration_email_label">Email ID</string>
    <string name="registration_error_msg_empty_confirm_password">Please confirm your password</string>
    <string name="registration_error_msg_empty_email">Please enter your email id</string>
    <string name="registration_error_msg_empty_mobile">Please enter your mobile number</string>
    <string name="registration_error_msg_empty_password">Please enter your password</string>
    <string name="registration_error_msg_password_mismatch">Please enter the same password here as above</string>
    <string name="registration_heading_text">Registration</string>
    <string name="registration_mobile_label">Mobile Number</string>
    <string name="registration_password_label">Password</string>
</resources>