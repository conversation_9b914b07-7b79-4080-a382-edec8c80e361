#Tue Aug 05 20:46:43 PKT 2025
base.0=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeExtDexStagingSumniPos2Debug\\classes.dex
base.1=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\0\\classes.dex
base.10=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\3\\classes.dex
base.11=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\4\\classes.dex
base.12=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\5\\classes.dex
base.13=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\6\\classes.dex
base.14=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\7\\classes.dex
base.15=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\8\\classes.dex
base.16=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\9\\classes.dex
base.17=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeExtDexStagingSumniPos2Debug\\classes2.dex
base.18=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeExtDexStagingSumniPos2Debug\\classes3.dex
base.19=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeExtDexStagingSumniPos2Debug\\classes4.dex
base.2=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\10\\classes.dex
base.20=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeExtDexStagingSumniPos2Debug\\classes5.dex
base.21=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\0\\classes2.dex
base.3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\11\\classes.dex
base.4=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\12\\classes.dex
base.5=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\13\\classes.dex
base.6=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\14\\classes.dex
base.7=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\15\\classes.dex
base.8=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\1\\classes.dex
base.9=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\dex\\stagingSumniPos2Debug\\mergeProjectDexStagingSumniPos2Debug\\2\\classes.dex
path.0=classes.dex
path.1=0/classes.dex
path.10=3/classes.dex
path.11=4/classes.dex
path.12=5/classes.dex
path.13=6/classes.dex
path.14=7/classes.dex
path.15=8/classes.dex
path.16=9/classes.dex
path.17=classes2.dex
path.18=classes3.dex
path.19=classes4.dex
path.2=10/classes.dex
path.20=classes5.dex
path.21=0/classes2.dex
path.3=11/classes.dex
path.4=12/classes.dex
path.5=13/classes.dex
path.6=14/classes.dex
path.7=15/classes.dex
path.8=1/classes.dex
path.9=2/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.10=classes11.dex
renamed.11=classes12.dex
renamed.12=classes13.dex
renamed.13=classes14.dex
renamed.14=classes15.dex
renamed.15=classes16.dex
renamed.16=classes17.dex
renamed.17=classes18.dex
renamed.18=classes19.dex
renamed.19=classes20.dex
renamed.2=classes3.dex
renamed.20=classes21.dex
renamed.21=classes22.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
renamed.9=classes10.dex
