package com.thedasagroup.suminative.data.database;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CategoryRepository_Factory implements Factory<CategoryRepository> {
  private final Provider<DatabaseManager> databaseManagerProvider;

  public CategoryRepository_Factory(Provider<DatabaseManager> databaseManagerProvider) {
    this.databaseManagerProvider = databaseManagerProvider;
  }

  @Override
  public CategoryRepository get() {
    return newInstance(databaseManagerProvider.get());
  }

  public static CategoryRepository_Factory create(
      Provider<DatabaseManager> databaseManagerProvider) {
    return new CategoryRepository_Factory(databaseManagerProvider);
  }

  public static CategoryRepository newInstance(DatabaseManager databaseManager) {
    return new CategoryRepository(databaseManager);
  }
}
