package com.thedasagroup.suminative.ui.products;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000x\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u000b\n\u0002\u0010\u0006\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0011\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u00d4\u0001\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\b2&\u0010\u000b\u001a\"\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0006\u0012\u0004\u0018\u00010\u000f\u0012\u0004\u0012\u00020\u00010\f2\u0018\u0010\u0010\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00010\u00112\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\u0016\u001a\u00020\u0017H\u0007\u001a\u00cc\u0001\u0010\u0018\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\u001e\u0010\u000b\u001a\u001a\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00192\u0018\u0010\u0010\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00010\u00112\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\u0016\u001a\u00020\u0017H\u0007\u001aN\u0010\u001a\u001a\u00020\u00012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001d0\u001c2\u0006\u0010\u001e\u001a\u00020\r2\u0012\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0003\u001a@\u0010!\u001a\u00020\u00012\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001d0\u001c2\u0006\u0010\u001e\u001a\u00020\r2\u0012\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0007\u001a&\u0010\"\u001a\u00020\u00012\u0006\u0010#\u001a\u00020\u001d2\u0006\u0010$\u001a\u00020%2\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0007\u001a\u0016\u0010\'\u001a\u00020\u00012\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0007\u001a\b\u0010(\u001a\u00020\u0001H\u0003\u001az\u0010)\u001a\u00020\u00012\u0006\u0010*\u001a\u00020\u00062\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00052\u001e\u0010\u000b\u001a\u001a\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00192\u0018\u0010\u0010\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00010\u00112\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\u0016\u001a\u00020\u0017H\u0003\u001a\u0010\u0010+\u001a\u00020\u00012\u0006\u0010,\u001a\u00020\u000eH\u0003\u001a*\u0010-\u001a\u00020\u00012\u0006\u0010.\u001a\u00020\r2\u0006\u0010/\u001a\u00020\u00122\u0006\u00100\u001a\u0002012\b\b\u0002\u00102\u001a\u00020%H\u0003\u001a\u0010\u00103\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0003\u001a3\u00104\u001a\u00020\u00012\u0006\u00105\u001a\u00020\u00122\u0006\u00106\u001a\u00020\u00122\b\b\u0002\u00107\u001a\u00020%2\b\b\u0002\u00108\u001a\u000209H\u0003\u00a2\u0006\u0004\b:\u0010;\u001a\\\u0010<\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\u0016\u001a\u00020\u0017H\u0003\u001a&\u0010=\u001a\u00020\u00012\b\b\u0002\u0010>\u001a\u00020\r2\u0012\u0010?\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a2\u0010@\u001a\u00020\u00012\u0006\u0010A\u001a\u00020\u00122\f\u0010B\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\u0012\u0010C\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a&\u0010D\u001a\u00020\u00012\b\b\u0002\u0010>\u001a\u00020\r2\u0012\u0010?\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a\u0010\u0010E\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a4\u0010F\u001a\u00020\u00012\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0007\u001a^\u0010G\u001a\u00020\u00012\u0006\u0010*\u001a\u00020\u00062\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00052\u001e\u0010\u000b\u001a\u001a\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00192\u0018\u0010\u0010\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00010\u0011H\u0007\u001a2\u0010H\u001a\u00020\u00012\u0006\u0010A\u001a\u00020\u00122\f\u0010B\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\u0012\u0010C\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a,\u0010I\u001a\u00020\u00012\u0006\u00105\u001a\u00020\u00122\u0006\u00106\u001a\u00020\u00122\b\b\u0002\u0010J\u001a\u00020K2\b\b\u0002\u00107\u001a\u00020%H\u0007\u001a*\u0010L\u001a\u00020\u00012\f\u0010B\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\u0012\u0010C\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u00a8\u0006M"}, d2 = {"CartScreenFigma", "", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "onRemoveItem", "Lkotlin/Function1;", "Lcom/thedasagroup/suminative/data/model/request/order/Cart;", "closeCart", "Lkotlin/Function0;", "placeOrderCash", "placeOrderCard", "onUpdateStock", "Lkotlin/Function4;", "", "Lcom/thedasagroup/suminative/data/model/request/order/StoreItem;", "Lcom/thedasagroup/suminative/data/model/response/options_details/OptionDetails;", "onUpdateNotes", "Lkotlin/Function2;", "", "onVoidItem", "onSplitBillClick", "onCloudPrintClick", "productsScreenViewModel", "Lcom/thedasagroup/suminative/ui/products/ProductsScreenViewModel;", "MobileCartScreen", "Lkotlin/Function3;", "MobileCartHeaderWithCourseTabs", "courses", "", "Lcom/thedasagroup/suminative/ui/products/Course;", "selectedCourseIndex", "onCourseSelected", "onAddCourse", "CourseSelectionTabs", "CourseTab", "course", "isSelected", "", "onClick", "AddCourseButton", "MobileEmptyCart", "MobileCartItemCard", "cartItem", "MobileCartItemDetails", "storeItem", "MobileCartItemRow", "quantity", "name", "price", "", "isExtra", "MobileCartSummary", "MobileSummaryRow", "title", "value", "isBold", "valueColor", "Landroidx/compose/ui/graphics/Color;", "MobileSummaryRow-g2O1Hgs", "(Ljava/lang/String;Ljava/lang/String;ZJ)V", "MobileOrderTotalSticky", "MobileStockUpdateCounterCart", "initialStock", "onStockChange", "MobileOrderNotesDialog", "initialNotes", "onDismiss", "onConfirm", "StockUpdateCounterCart", "CartSummery", "OrderTotalSticky", "CartItemCard", "OrderNotesDialog", "TotalCartFigma", "style", "Landroidx/compose/ui/text/TextStyle;", "SplitBillDialog", "app_stagingSumniPos2Debug"})
public final class CartScreenFigmaKt {
    
    @androidx.compose.runtime.Composable()
    public static final void CartScreenFigma(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.order.Cart, kotlin.Unit> onRemoveItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> closeCart, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> placeOrderCash, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> placeOrderCard, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function4<? super java.lang.Integer, ? super com.thedasagroup.suminative.data.model.request.order.StoreItem, ? super com.thedasagroup.suminative.data.model.request.order.Cart, ? super com.thedasagroup.suminative.data.model.response.options_details.OptionDetails, kotlin.Unit> onUpdateStock, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.thedasagroup.suminative.data.model.request.order.Cart, ? super java.lang.String, kotlin.Unit> onUpdateNotes, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.order.Cart, kotlin.Unit> onVoidItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onSplitBillClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.order.Order, kotlin.Unit> onCloudPrintClick, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.ProductsScreenViewModel productsScreenViewModel) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class, androidx.compose.foundation.ExperimentalFoundationApi.class})
    @androidx.compose.runtime.Composable()
    public static final void MobileCartScreen(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.order.Cart, kotlin.Unit> onRemoveItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> closeCart, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> placeOrderCash, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> placeOrderCard, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.Integer, ? super com.thedasagroup.suminative.data.model.request.order.StoreItem, ? super com.thedasagroup.suminative.data.model.request.order.Cart, kotlin.Unit> onUpdateStock, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.thedasagroup.suminative.data.model.request.order.Cart, ? super java.lang.String, kotlin.Unit> onUpdateNotes, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onSplitBillClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.order.Order, kotlin.Unit> onCloudPrintClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.order.Cart, kotlin.Unit> onVoidItem, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.ProductsScreenViewModel productsScreenViewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MobileCartHeaderWithCourseTabs(kotlin.jvm.functions.Function0<kotlin.Unit> closeCart, java.util.List<com.thedasagroup.suminative.ui.products.Course> courses, int selectedCourseIndex, kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onCourseSelected, kotlin.jvm.functions.Function0<kotlin.Unit> onAddCourse) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CourseSelectionTabs(@org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.ui.products.Course> courses, int selectedCourseIndex, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onCourseSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onAddCourse) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CourseTab(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.Course course, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddCourseButton(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MobileEmptyCart() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MobileCartItemCard(com.thedasagroup.suminative.data.model.request.order.Cart cartItem, kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.order.Cart, kotlin.Unit> onRemoveItem, kotlin.jvm.functions.Function3<? super java.lang.Integer, ? super com.thedasagroup.suminative.data.model.request.order.StoreItem, ? super com.thedasagroup.suminative.data.model.request.order.Cart, kotlin.Unit> onUpdateStock, kotlin.jvm.functions.Function2<? super com.thedasagroup.suminative.data.model.request.order.Cart, ? super java.lang.String, kotlin.Unit> onUpdateNotes, kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.order.Cart, kotlin.Unit> onVoidItem, com.thedasagroup.suminative.ui.products.ProductsScreenViewModel productsScreenViewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MobileCartItemDetails(com.thedasagroup.suminative.data.model.request.order.StoreItem storeItem) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MobileCartItemRow(int quantity, java.lang.String name, double price, boolean isExtra) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MobileCartSummary(com.thedasagroup.suminative.data.model.request.order.Order order) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MobileOrderTotalSticky(com.thedasagroup.suminative.data.model.request.order.Order order, kotlin.jvm.functions.Function0<kotlin.Unit> placeOrderCash, kotlin.jvm.functions.Function0<kotlin.Unit> placeOrderCard, kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onSplitBillClick, kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.order.Order, kotlin.Unit> onCloudPrintClick, com.thedasagroup.suminative.ui.products.ProductsScreenViewModel productsScreenViewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MobileStockUpdateCounterCart(int initialStock, kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onStockChange) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void MobileOrderNotesDialog(java.lang.String initialNotes, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onConfirm) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void StockUpdateCounterCart(int initialStock, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onStockChange) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CartSummery(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void OrderTotalSticky(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.ProductsScreenViewModel productsScreenViewModel, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> placeOrderCash, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> placeOrderCard) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CartItemCard(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Cart cartItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.order.Cart, kotlin.Unit> onRemoveItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.Integer, ? super com.thedasagroup.suminative.data.model.request.order.StoreItem, ? super com.thedasagroup.suminative.data.model.request.order.Cart, kotlin.Unit> onUpdateStock, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.thedasagroup.suminative.data.model.request.order.Cart, ? super java.lang.String, kotlin.Unit> onUpdateNotes) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void OrderNotesDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String initialNotes, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onConfirm) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TotalCartFigma(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String value, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.text.TextStyle style, boolean isBold) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void SplitBillDialog(kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onConfirm) {
    }
}