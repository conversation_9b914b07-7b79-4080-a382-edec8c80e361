package com.thedasagroup.suminative.ui.products;

import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DownloadProductsViewModel_Factory_Impl implements DownloadProductsViewModel.Factory {
  private final DownloadProductsViewModel_Factory delegateFactory;

  DownloadProductsViewModel_Factory_Impl(DownloadProductsViewModel_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public DownloadProductsViewModel create(DownloadProductsState state) {
    return delegateFactory.get(state);
  }

  public static Provider<DownloadProductsViewModel.Factory> create(
      DownloadProductsViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new DownloadProductsViewModel_Factory_Impl(delegateFactory));
  }

  public static dagger.internal.Provider<DownloadProductsViewModel.Factory> createFactoryProvider(
      DownloadProductsViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new DownloadProductsViewModel_Factory_Impl(delegateFactory));
  }
}
