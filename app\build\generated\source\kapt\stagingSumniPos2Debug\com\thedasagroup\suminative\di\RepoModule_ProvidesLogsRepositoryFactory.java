package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.LogsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesLogsRepositoryFactory implements Factory<LogsRepository> {
  private final Provider<Prefs> prefsProvider;

  public RepoModule_ProvidesLogsRepositoryFactory(Provider<Prefs> prefsProvider) {
    this.prefsProvider = prefsProvider;
  }

  @Override
  public LogsRepository get() {
    return providesLogsRepository(prefsProvider.get());
  }

  public static RepoModule_ProvidesLogsRepositoryFactory create(Provider<Prefs> prefsProvider) {
    return new RepoModule_ProvidesLogsRepositoryFactory(prefsProvider);
  }

  public static LogsRepository providesLogsRepository(Prefs prefs) {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesLogsRepository(prefs));
  }
}
