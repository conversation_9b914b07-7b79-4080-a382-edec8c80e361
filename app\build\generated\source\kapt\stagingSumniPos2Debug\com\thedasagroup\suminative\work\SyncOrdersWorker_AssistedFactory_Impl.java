package com.thedasagroup.suminative.work;

import android.content.Context;
import androidx.work.WorkerParameters;
import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SyncOrdersWorker_AssistedFactory_Impl implements SyncOrdersWorker_AssistedFactory {
  private final SyncOrdersWorker_Factory delegateFactory;

  SyncOrdersWorker_AssistedFactory_Impl(SyncOrdersWorker_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public SyncOrdersWorker create(Context context, WorkerParameters parameters) {
    return delegateFactory.get(context, parameters);
  }

  public static Provider<SyncOrdersWorker_AssistedFactory> create(
      SyncOrdersWorker_Factory delegateFactory) {
    return InstanceFactory.create(new SyncOrdersWorker_AssistedFactory_Impl(delegateFactory));
  }

  public static dagger.internal.Provider<SyncOrdersWorker_AssistedFactory> createFactoryProvider(
      SyncOrdersWorker_Factory delegateFactory) {
    return InstanceFactory.create(new SyncOrdersWorker_AssistedFactory_Impl(delegateFactory));
  }
}
