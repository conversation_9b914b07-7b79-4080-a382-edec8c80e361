package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.ReservationsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesReservationsRepositoryFactory implements Factory<ReservationsRepository> {
  @Override
  public ReservationsRepository get() {
    return providesReservationsRepository();
  }

  public static RepoModule_ProvidesReservationsRepositoryFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static ReservationsRepository providesReservationsRepository() {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesReservationsRepository());
  }

  private static final class InstanceHolder {
    static final RepoModule_ProvidesReservationsRepositoryFactory INSTANCE = new RepoModule_ProvidesReservationsRepositoryFactory();
  }
}
