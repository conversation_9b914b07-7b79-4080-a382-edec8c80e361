package com.thedasagroup.suminative.work;

import android.content.Context;
import androidx.work.WorkerParameters;
import com.thedasagroup.suminative.domain.orders.SyncOrdersUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SyncOrdersWorker_Factory {
  private final Provider<SyncOrdersUseCase> syncOrdersUseCaseProvider;

  public SyncOrdersWorker_Factory(Provider<SyncOrdersUseCase> syncOrdersUseCaseProvider) {
    this.syncOrdersUseCaseProvider = syncOrdersUseCaseProvider;
  }

  public SyncOrdersWorker get(Context appContext, WorkerParameters params) {
    return newInstance(syncOrdersUseCaseProvider.get(), appContext, params);
  }

  public static SyncOrdersWorker_Factory create(
      Provider<SyncOrdersUseCase> syncOrdersUseCaseProvider) {
    return new SyncOrdersWorker_Factory(syncOrdersUseCaseProvider);
  }

  public static SyncOrdersWorker newInstance(SyncOrdersUseCase syncOrdersUseCase,
      Context appContext, WorkerParameters params) {
    return new SyncOrdersWorker(syncOrdersUseCase, appContext, params);
  }
}
