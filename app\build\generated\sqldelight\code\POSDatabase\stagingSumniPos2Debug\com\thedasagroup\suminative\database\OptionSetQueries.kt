package com.thedasagroup.suminative.database

import app.cash.sqldelight.Query
import app.cash.sqldelight.TransacterImpl
import app.cash.sqldelight.db.QueryResult
import app.cash.sqldelight.db.SqlCursor
import app.cash.sqldelight.db.SqlDriver
import kotlin.Any
import kotlin.Long
import kotlin.String

public class OptionSetQueries(
  driver: SqlDriver,
) : TransacterImpl(driver) {
  public fun <T : Any> getOptionSetsByProduct(productId: Long, mapper: (
    id: Long,
    optionSetId: Long?,
    productId: Long,
    name: String?,
    condition: Long?,
    customNumber: Long?,
    displayOrder: Long?,
    itemId: Long?,
    status: Long?,
    variantType: Long?,
    storeId: Long,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = GetOptionSetsByProductQuery(productId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getLong(2)!!,
      cursor.getString(3),
      cursor.getLong(4),
      cursor.getLong(5),
      cursor.getLong(6),
      cursor.getLong(7),
      cursor.getLong(8),
      cursor.getLong(9),
      cursor.getLong(10)!!,
      cursor.getLong(11)!!,
      cursor.getLong(12)!!,
      cursor.getLong(13)!!
    )
  }

  public fun getOptionSetsByProduct(productId: Long): Query<OptionSetEntity> =
      getOptionSetsByProduct(productId) { id, optionSetId, productId_, name, condition,
      customNumber, displayOrder, itemId, status, variantType, storeId, createdAt, updatedAt,
      synced ->
    OptionSetEntity(
      id,
      optionSetId,
      productId_,
      name,
      condition,
      customNumber,
      displayOrder,
      itemId,
      status,
      variantType,
      storeId,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> getOptionSetsByStore(storeId: Long, mapper: (
    id: Long,
    optionSetId: Long?,
    productId: Long,
    name: String?,
    condition: Long?,
    customNumber: Long?,
    displayOrder: Long?,
    itemId: Long?,
    status: Long?,
    variantType: Long?,
    storeId: Long,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = GetOptionSetsByStoreQuery(storeId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getLong(2)!!,
      cursor.getString(3),
      cursor.getLong(4),
      cursor.getLong(5),
      cursor.getLong(6),
      cursor.getLong(7),
      cursor.getLong(8),
      cursor.getLong(9),
      cursor.getLong(10)!!,
      cursor.getLong(11)!!,
      cursor.getLong(12)!!,
      cursor.getLong(13)!!
    )
  }

  public fun getOptionSetsByStore(storeId: Long): Query<OptionSetEntity> =
      getOptionSetsByStore(storeId) { id, optionSetId, productId, name, condition, customNumber,
      displayOrder, itemId, status, variantType, storeId_, createdAt, updatedAt, synced ->
    OptionSetEntity(
      id,
      optionSetId,
      productId,
      name,
      condition,
      customNumber,
      displayOrder,
      itemId,
      status,
      variantType,
      storeId_,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun <T : Any> getOptionSetById(optionSetId: Long?, mapper: (
    id: Long,
    optionSetId: Long?,
    productId: Long,
    name: String?,
    condition: Long?,
    customNumber: Long?,
    displayOrder: Long?,
    itemId: Long?,
    status: Long?,
    variantType: Long?,
    storeId: Long,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ) -> T): Query<T> = GetOptionSetByIdQuery(optionSetId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getLong(1),
      cursor.getLong(2)!!,
      cursor.getString(3),
      cursor.getLong(4),
      cursor.getLong(5),
      cursor.getLong(6),
      cursor.getLong(7),
      cursor.getLong(8),
      cursor.getLong(9),
      cursor.getLong(10)!!,
      cursor.getLong(11)!!,
      cursor.getLong(12)!!,
      cursor.getLong(13)!!
    )
  }

  public fun getOptionSetById(optionSetId: Long?): Query<OptionSetEntity> =
      getOptionSetById(optionSetId) { id, optionSetId_, productId, name, condition, customNumber,
      displayOrder, itemId, status, variantType, storeId, createdAt, updatedAt, synced ->
    OptionSetEntity(
      id,
      optionSetId_,
      productId,
      name,
      condition,
      customNumber,
      displayOrder,
      itemId,
      status,
      variantType,
      storeId,
      createdAt,
      updatedAt,
      synced
    )
  }

  public fun countOptionSetsByProduct(productId: Long): Query<Long> =
      CountOptionSetsByProductQuery(productId) { cursor ->
    cursor.getLong(0)!!
  }

  public fun countTotalOptionSets(): Query<Long> = Query(-1_630_560_110, arrayOf("OptionSetEntity"),
      driver, "OptionSet.sq", "countTotalOptionSets", "SELECT COUNT(*) FROM OptionSetEntity") {
      cursor ->
    cursor.getLong(0)!!
  }

  /**
   * @return The number of rows updated.
   */
  public fun insertOptionSet(
    optionSetId: Long?,
    productId: Long,
    name: String?,
    condition: Long?,
    customNumber: Long?,
    displayOrder: Long?,
    itemId: Long?,
    status: Long?,
    variantType: Long?,
    storeId: Long,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ): QueryResult<Long> {
    val result = driver.execute(84_607_421, """
        |INSERT INTO OptionSetEntity (
        |    optionSetId, productId, name, condition, customNumber, displayOrder,
        |    itemId, status, variantType, storeId, createdAt, updatedAt, synced
        |) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """.trimMargin(), 13) {
          bindLong(0, optionSetId)
          bindLong(1, productId)
          bindString(2, name)
          bindLong(3, condition)
          bindLong(4, customNumber)
          bindLong(5, displayOrder)
          bindLong(6, itemId)
          bindLong(7, status)
          bindLong(8, variantType)
          bindLong(9, storeId)
          bindLong(10, createdAt)
          bindLong(11, updatedAt)
          bindLong(12, synced)
        }
    notifyQueries(84_607_421) { emit ->
      emit("OptionSetEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun insertOrReplaceOptionSet(
    optionSetId: Long?,
    productId: Long,
    name: String?,
    condition: Long?,
    customNumber: Long?,
    displayOrder: Long?,
    itemId: Long?,
    status: Long?,
    variantType: Long?,
    storeId: Long,
    createdAt: Long,
    updatedAt: Long,
    synced: Long,
  ): QueryResult<Long> {
    val result = driver.execute(997_067_596, """
        |INSERT OR REPLACE INTO OptionSetEntity (
        |    optionSetId, productId, name, condition, customNumber, displayOrder,
        |    itemId, status, variantType, storeId, createdAt, updatedAt, synced
        |) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """.trimMargin(), 13) {
          bindLong(0, optionSetId)
          bindLong(1, productId)
          bindString(2, name)
          bindLong(3, condition)
          bindLong(4, customNumber)
          bindLong(5, displayOrder)
          bindLong(6, itemId)
          bindLong(7, status)
          bindLong(8, variantType)
          bindLong(9, storeId)
          bindLong(10, createdAt)
          bindLong(11, updatedAt)
          bindLong(12, synced)
        }
    notifyQueries(997_067_596) { emit ->
      emit("OptionSetEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun updateOptionSet(
    name: String?,
    condition: Long?,
    customNumber: Long?,
    displayOrder: Long?,
    status: Long?,
    variantType: Long?,
    updatedAt: Long,
    synced: Long,
    optionSetId: Long?,
  ): QueryResult<Long> {
    val result = driver.execute(null, """
        |UPDATE OptionSetEntity 
        |SET name = ?, condition = ?, customNumber = ?, displayOrder = ?,
        |    status = ?, variantType = ?, updatedAt = ?, synced = ?
        |WHERE optionSetId ${ if (optionSetId == null) "IS" else "=" } ?
        """.trimMargin(), 9) {
          bindString(0, name)
          bindLong(1, condition)
          bindLong(2, customNumber)
          bindLong(3, displayOrder)
          bindLong(4, status)
          bindLong(5, variantType)
          bindLong(6, updatedAt)
          bindLong(7, synced)
          bindLong(8, optionSetId)
        }
    notifyQueries(-1_072_210_515) { emit ->
      emit("OptionSetEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun deleteOptionSetsByProduct(productId: Long): QueryResult<Long> {
    val result = driver.execute(-1_274_517_840,
        """DELETE FROM OptionSetEntity WHERE productId = ?""", 1) {
          bindLong(0, productId)
        }
    notifyQueries(-1_274_517_840) { emit ->
      emit("OptionSetEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun deleteAllOptionSets(): QueryResult<Long> {
    val result = driver.execute(-179_065_019, """DELETE FROM OptionSetEntity""", 0)
    notifyQueries(-179_065_019) { emit ->
      emit("OptionSetEntity")
    }
    return result
  }

  private inner class GetOptionSetsByProductQuery<out T : Any>(
    public val productId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OptionSetEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OptionSetEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-1_621_598_029,
        """SELECT OptionSetEntity.id, OptionSetEntity.optionSetId, OptionSetEntity.productId, OptionSetEntity.name, OptionSetEntity.condition, OptionSetEntity.customNumber, OptionSetEntity.displayOrder, OptionSetEntity.itemId, OptionSetEntity.status, OptionSetEntity.variantType, OptionSetEntity.storeId, OptionSetEntity.createdAt, OptionSetEntity.updatedAt, OptionSetEntity.synced FROM OptionSetEntity WHERE productId = ? ORDER BY displayOrder ASC""",
        mapper, 1) {
      bindLong(0, productId)
    }

    override fun toString(): String = "OptionSet.sq:getOptionSetsByProduct"
  }

  private inner class GetOptionSetsByStoreQuery<out T : Any>(
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OptionSetEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OptionSetEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(783_265_189,
        """SELECT OptionSetEntity.id, OptionSetEntity.optionSetId, OptionSetEntity.productId, OptionSetEntity.name, OptionSetEntity.condition, OptionSetEntity.customNumber, OptionSetEntity.displayOrder, OptionSetEntity.itemId, OptionSetEntity.status, OptionSetEntity.variantType, OptionSetEntity.storeId, OptionSetEntity.createdAt, OptionSetEntity.updatedAt, OptionSetEntity.synced FROM OptionSetEntity WHERE storeId = ? ORDER BY displayOrder ASC""",
        mapper, 1) {
      bindLong(0, storeId)
    }

    override fun toString(): String = "OptionSet.sq:getOptionSetsByStore"
  }

  private inner class GetOptionSetByIdQuery<out T : Any>(
    public val optionSetId: Long?,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OptionSetEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OptionSetEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(null,
        """SELECT OptionSetEntity.id, OptionSetEntity.optionSetId, OptionSetEntity.productId, OptionSetEntity.name, OptionSetEntity.condition, OptionSetEntity.customNumber, OptionSetEntity.displayOrder, OptionSetEntity.itemId, OptionSetEntity.status, OptionSetEntity.variantType, OptionSetEntity.storeId, OptionSetEntity.createdAt, OptionSetEntity.updatedAt, OptionSetEntity.synced FROM OptionSetEntity WHERE optionSetId ${ if (optionSetId == null) "IS" else "=" } ?""",
        mapper, 1) {
      bindLong(0, optionSetId)
    }

    override fun toString(): String = "OptionSet.sq:getOptionSetById"
  }

  private inner class CountOptionSetsByProductQuery<out T : Any>(
    public val productId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("OptionSetEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("OptionSetEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-28_278_630,
        """SELECT COUNT(*) FROM OptionSetEntity WHERE productId = ?""", mapper, 1) {
      bindLong(0, productId)
    }

    override fun toString(): String = "OptionSet.sq:countOptionSetsByProduct"
  }
}
