package com.thedasagroup.suminative.ui.service;

import com.thedasagroup.suminative.data.prefs.Prefs;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class MySocketJobService_MembersInjector implements MembersInjector<MySocketJobService> {
  private final Provider<Prefs> prefsProvider;

  public MySocketJobService_MembersInjector(Provider<Prefs> prefsProvider) {
    this.prefsProvider = prefsProvider;
  }

  public static MembersInjector<MySocketJobService> create(Provider<Prefs> prefsProvider) {
    return new MySocketJobService_MembersInjector(prefsProvider);
  }

  @Override
  public void injectMembers(MySocketJobService instance) {
    injectPrefs(instance, prefsProvider.get());
  }

  @InjectedFieldSignature("com.thedasagroup.suminative.ui.service.MySocketJobService.prefs")
  public static void injectPrefs(MySocketJobService instance, Prefs prefs) {
    instance.prefs = prefs;
  }
}
