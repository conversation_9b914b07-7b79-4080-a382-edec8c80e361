package com.thedasagroup.suminative.di;

import android.content.Context;
import com.thedasagroup.suminative.data.database.DatabaseManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesDatabaseManagerFactory implements Factory<DatabaseManager> {
  private final Provider<Context> contextProvider;

  public RepoModule_ProvidesDatabaseManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public DatabaseManager get() {
    return providesDatabaseManager(contextProvider.get());
  }

  public static RepoModule_ProvidesDatabaseManagerFactory create(
      Provider<Context> contextProvider) {
    return new RepoModule_ProvidesDatabaseManagerFactory(contextProvider);
  }

  public static DatabaseManager providesDatabaseManager(Context context) {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesDatabaseManager(context));
  }
}
