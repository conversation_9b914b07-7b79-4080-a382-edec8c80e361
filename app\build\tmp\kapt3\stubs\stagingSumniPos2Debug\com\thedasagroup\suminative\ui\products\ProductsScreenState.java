package com.thedasagroup.suminative.ui.products;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u009a\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\bE\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u00d9\u0002\u0012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\t\u0012\b\b\u0002\u0010\u000b\u001a\u00020\f\u0012\u0014\b\u0002\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\f0\u000e\u0012\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u0003\u0012\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\u0003\u0012\u000e\b\u0002\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\u0003\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0016\u0012\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u0018\u0012\b\b\u0002\u0010\u0019\u001a\u00020\u001a\u0012\b\b\u0002\u0010\u001b\u001a\u00020\u001a\u0012\u000e\b\u0002\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001d0\u0003\u0012\b\b\u0002\u0010\u001e\u001a\u00020\u001a\u0012\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010 \u0012\b\b\u0002\u0010!\u001a\u00020\u001a\u0012\b\b\u0002\u0010\"\u001a\u00020#\u0012\u000e\b\u0002\u0010$\u001a\b\u0012\u0004\u0012\u00020&0%\u0012\b\b\u0002\u0010\'\u001a\u00020\u0007\u0012\u001a\b\u0002\u0010(\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\n\u0012\b\u0012\u0004\u0012\u00020)0%0\u000e\u0012\u0014\b\u0002\u0010*\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020+0\u000e\u0012\u000e\b\u0002\u0010,\u001a\b\u0012\u0004\u0012\u00020-0%\u00a2\u0006\u0004\b.\u0010/J\u0006\u0010Q\u001a\u00020\fJ\r\u0010R\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0002\u0010SJ\f\u0010T\u001a\b\u0012\u0004\u0012\u00020)0%J\u0006\u0010U\u001a\u00020+J\f\u0010V\u001a\b\u0012\u0004\u0012\u00020)0%J\u0012\u0010W\u001a\u000e\u0012\u0004\u0012\u00020+\u0012\u0004\u0012\u00020\u00070\u000eJ\u000f\u0010X\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u0010Y\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010Z\u001a\u00020\u0007H\u00c6\u0003J\u000b\u0010[\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u000b\u0010\\\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\t\u0010]\u001a\u00020\fH\u00c6\u0003J\u0015\u0010^\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\f0\u000eH\u00c6\u0003J\u000f\u0010_\u001a\b\u0012\u0004\u0012\u00020\u00100\u0003H\u00c6\u0003J\u000f\u0010`\u001a\b\u0012\u0004\u0012\u00020\u00120\u0003H\u00c6\u0003J\u000f\u0010a\u001a\b\u0012\u0004\u0012\u00020\u00140\u0003H\u00c6\u0003J\t\u0010b\u001a\u00020\u0016H\u00c6\u0003J\u000b\u0010c\u001a\u0004\u0018\u00010\u0018H\u00c6\u0003J\t\u0010d\u001a\u00020\u001aH\u00c6\u0003J\t\u0010e\u001a\u00020\u001aH\u00c6\u0003J\u000f\u0010f\u001a\b\u0012\u0004\u0012\u00020\u001d0\u0003H\u00c6\u0003J\t\u0010g\u001a\u00020\u001aH\u00c6\u0003J\u000b\u0010h\u001a\u0004\u0018\u00010 H\u00c6\u0003J\t\u0010i\u001a\u00020\u001aH\u00c6\u0003J\t\u0010j\u001a\u00020#H\u00c6\u0003J\u000f\u0010k\u001a\b\u0012\u0004\u0012\u00020&0%H\u00c6\u0003J\t\u0010l\u001a\u00020\u0007H\u00c6\u0003J\u001b\u0010m\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\n\u0012\b\u0012\u0004\u0012\u00020)0%0\u000eH\u00c6\u0003J\u0015\u0010n\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020+0\u000eH\u00c6\u0003J\u000f\u0010o\u001a\b\u0012\u0004\u0012\u00020-0%H\u00c6\u0003J\u00db\u0002\u0010p\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\t2\b\b\u0002\u0010\u000b\u001a\u00020\f2\u0014\b\u0002\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\f0\u000e2\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\u00032\u000e\b\u0002\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\u00032\b\b\u0002\u0010\u0015\u001a\u00020\u00162\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00182\b\b\u0002\u0010\u0019\u001a\u00020\u001a2\b\b\u0002\u0010\u001b\u001a\u00020\u001a2\u000e\b\u0002\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001d0\u00032\b\b\u0002\u0010\u001e\u001a\u00020\u001a2\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010 2\b\b\u0002\u0010!\u001a\u00020\u001a2\b\b\u0002\u0010\"\u001a\u00020#2\u000e\b\u0002\u0010$\u001a\b\u0012\u0004\u0012\u00020&0%2\b\b\u0002\u0010\'\u001a\u00020\u00072\u001a\b\u0002\u0010(\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\n\u0012\b\u0012\u0004\u0012\u00020)0%0\u000e2\u0014\b\u0002\u0010*\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020+0\u000e2\u000e\b\u0002\u0010,\u001a\b\u0012\u0004\u0012\u00020-0%H\u00c6\u0001J\u0013\u0010q\u001a\u00020\u001a2\b\u0010r\u001a\u0004\u0018\u00010sH\u00d6\u0003J\t\u0010t\u001a\u00020\u0007H\u00d6\u0001J\t\u0010u\u001a\u00020vH\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u00101R\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u00101R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u00104R\u0013\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u00106R\u0013\u0010\n\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u00106R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u00108R\u001d\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\f0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010:R\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u00101R\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u00101R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u00101R\u0011\u0010\u0015\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u0010?R\u0013\u0010\u0017\u001a\u0004\u0018\u00010\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010@R\u0011\u0010\u0019\u001a\u00020\u001a\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u0010BR\u0011\u0010\u001b\u001a\u00020\u001a\u00a2\u0006\b\n\u0000\u001a\u0004\bC\u0010BR\u0017\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001d0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bD\u00101R\u0011\u0010\u001e\u001a\u00020\u001a\u00a2\u0006\b\n\u0000\u001a\u0004\bE\u0010BR\u0013\u0010\u001f\u001a\u0004\u0018\u00010 \u00a2\u0006\b\n\u0000\u001a\u0004\bF\u0010GR\u0011\u0010!\u001a\u00020\u001a\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u0010BR\u0011\u0010\"\u001a\u00020#\u00a2\u0006\b\n\u0000\u001a\u0004\bI\u0010JR\u0017\u0010$\u001a\b\u0012\u0004\u0012\u00020&0%\u00a2\u0006\b\n\u0000\u001a\u0004\bK\u0010LR\u0011\u0010\'\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\bM\u00104R#\u0010(\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\n\u0012\b\u0012\u0004\u0012\u00020)0%0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\bN\u0010:R\u001d\u0010*\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020+0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\bO\u0010:R\u0017\u0010,\u001a\b\u0012\u0004\u0012\u00020-0%\u00a2\u0006\b\n\u0000\u001a\u0004\bP\u0010L\u00a8\u0006w"}, d2 = {"Lcom/thedasagroup/suminative/ui/products/ProductsScreenState;", "Lcom/airbnb/mvrx/MavericksState;", "stockItemsResponse", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/stock/StockItemsResponse;", "stockResponse", "stock", "", "showUpdateStockDialog", "Lcom/thedasagroup/suminative/data/model/response/stock/StockItem;", "isBottomSheetVisible", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "tableOrders", "", "orderResponse", "Lcom/thedasagroup/suminative/data/model/response/order/OrderResponse2;", "salesResponse", "Lcom/thedasagroup/suminative/data/model/response/sales/SalesResponse;", "optionDetailsResponse", "Lcom/thedasagroup/suminative/data/model/response/options_details/OptionDetails;", "productTotal", "", "isShowPrintingPreview", "Lcom/thedasagroup/suminative/data/model/request/pagination/OrderItem2;", "shouldPrintInstant", "", "showCart", "salesReportResponse", "Lcom/thedasagroup/suminative/data/model/response/sales/SalesReportResponse;", "showSalesReportDialog", "salesRequest", "Lcom/thedasagroup/suminative/data/model/request/sales/SalesRequest;", "refreshing", "syncStatus", "Lcom/thedasagroup/suminative/work/SyncStatus;", "selectedTables", "", "Lcom/thedasagroup/suminative/ui/reservations/AreaTableSelectionHelper$AreaTableSelection;", "selectedTableIndex", "cartItemsWithCourses", "Lcom/thedasagroup/suminative/ui/products/CartItemWithCourse;", "selectedCourseFilter", "Lcom/thedasagroup/suminative/ui/products/CourseFilter;", "availableCourses", "Lcom/thedasagroup/suminative/ui/products/MealCourse;", "<init>", "(Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;ILcom/thedasagroup/suminative/data/model/response/stock/StockItem;Lcom/thedasagroup/suminative/data/model/response/stock/StockItem;Lcom/thedasagroup/suminative/data/model/request/order/Order;Ljava/util/Map;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;DLcom/thedasagroup/suminative/data/model/request/pagination/OrderItem2;ZZLcom/airbnb/mvrx/Async;ZLcom/thedasagroup/suminative/data/model/request/sales/SalesRequest;ZLcom/thedasagroup/suminative/work/SyncStatus;Ljava/util/List;ILjava/util/Map;Ljava/util/Map;Ljava/util/List;)V", "getStockItemsResponse", "()Lcom/airbnb/mvrx/Async;", "getStockResponse", "getStock", "()I", "getShowUpdateStockDialog", "()Lcom/thedasagroup/suminative/data/model/response/stock/StockItem;", "getOrder", "()Lcom/thedasagroup/suminative/data/model/request/order/Order;", "getTableOrders", "()Ljava/util/Map;", "getOrderResponse", "getSalesResponse", "getOptionDetailsResponse", "getProductTotal", "()D", "()Lcom/thedasagroup/suminative/data/model/request/pagination/OrderItem2;", "getShouldPrintInstant", "()Z", "getShowCart", "getSalesReportResponse", "getShowSalesReportDialog", "getSalesRequest", "()Lcom/thedasagroup/suminative/data/model/request/sales/SalesRequest;", "getRefreshing", "getSyncStatus", "()Lcom/thedasagroup/suminative/work/SyncStatus;", "getSelectedTables", "()Ljava/util/List;", "getSelectedTableIndex", "getCartItemsWithCourses", "getSelectedCourseFilter", "getAvailableCourses", "getCurrentTableOrder", "getCurrentTableId", "()Ljava/lang/Integer;", "getCurrentTableCartItemsWithCourses", "getCurrentTableCourseFilter", "getFilteredCartItems", "getCourseCounts", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component20", "component21", "component22", "component23", "component24", "copy", "equals", "other", "", "hashCode", "toString", "", "app_stagingSumniPos2Debug"})
public final class ProductsScreenState implements com.airbnb.mvrx.MavericksState {
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> stockItemsResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> stockResponse = null;
    private final int stock = 0;
    @org.jetbrains.annotations.Nullable()
    private final com.thedasagroup.suminative.data.model.response.stock.StockItem showUpdateStockDialog = null;
    @org.jetbrains.annotations.Nullable()
    private final com.thedasagroup.suminative.data.model.response.stock.StockItem isBottomSheetVisible = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.model.request.order.Order order = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.Integer, com.thedasagroup.suminative.data.model.request.order.Order> tableOrders = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.order.OrderResponse2> orderResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.sales.SalesResponse> salesResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.options_details.OptionDetails> optionDetailsResponse = null;
    private final double productTotal = 0.0;
    @org.jetbrains.annotations.Nullable()
    private final com.thedasagroup.suminative.data.model.request.pagination.OrderItem2 isShowPrintingPreview = null;
    private final boolean shouldPrintInstant = false;
    private final boolean showCart = false;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.sales.SalesReportResponse> salesReportResponse = null;
    private final boolean showSalesReportDialog = false;
    @org.jetbrains.annotations.Nullable()
    private final com.thedasagroup.suminative.data.model.request.sales.SalesRequest salesRequest = null;
    private final boolean refreshing = false;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.work.SyncStatus syncStatus = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection> selectedTables = null;
    private final int selectedTableIndex = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.Integer, java.util.List<com.thedasagroup.suminative.ui.products.CartItemWithCourse>> cartItemsWithCourses = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.Integer, com.thedasagroup.suminative.ui.products.CourseFilter> selectedCourseFilter = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.thedasagroup.suminative.ui.products.MealCourse> availableCourses = null;
    
    public ProductsScreenState(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> stockItemsResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> stockResponse, int stock, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.stock.StockItem showUpdateStockDialog, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.stock.StockItem isBottomSheetVisible, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, com.thedasagroup.suminative.data.model.request.order.Order> tableOrders, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.order.OrderResponse2> orderResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.sales.SalesResponse> salesResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.options_details.OptionDetails> optionDetailsResponse, double productTotal, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem2 isShowPrintingPreview, boolean shouldPrintInstant, boolean showCart, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.sales.SalesReportResponse> salesReportResponse, boolean showSalesReportDialog, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.sales.SalesRequest salesRequest, boolean refreshing, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.work.SyncStatus syncStatus, @org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection> selectedTables, int selectedTableIndex, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, ? extends java.util.List<com.thedasagroup.suminative.ui.products.CartItemWithCourse>> cartItemsWithCourses, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, ? extends com.thedasagroup.suminative.ui.products.CourseFilter> selectedCourseFilter, @org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.ui.products.MealCourse> availableCourses) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> getStockItemsResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> getStockResponse() {
        return null;
    }
    
    public final int getStock() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.stock.StockItem getShowUpdateStockDialog() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.stock.StockItem isBottomSheetVisible() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.request.order.Order getOrder() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.Integer, com.thedasagroup.suminative.data.model.request.order.Order> getTableOrders() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.order.OrderResponse2> getOrderResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.sales.SalesResponse> getSalesResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.options_details.OptionDetails> getOptionDetailsResponse() {
        return null;
    }
    
    public final double getProductTotal() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.request.pagination.OrderItem2 isShowPrintingPreview() {
        return null;
    }
    
    public final boolean getShouldPrintInstant() {
        return false;
    }
    
    public final boolean getShowCart() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.sales.SalesReportResponse> getSalesReportResponse() {
        return null;
    }
    
    public final boolean getShowSalesReportDialog() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.request.sales.SalesRequest getSalesRequest() {
        return null;
    }
    
    public final boolean getRefreshing() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.work.SyncStatus getSyncStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection> getSelectedTables() {
        return null;
    }
    
    public final int getSelectedTableIndex() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.Integer, java.util.List<com.thedasagroup.suminative.ui.products.CartItemWithCourse>> getCartItemsWithCourses() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.Integer, com.thedasagroup.suminative.ui.products.CourseFilter> getSelectedCourseFilter() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.thedasagroup.suminative.ui.products.MealCourse> getAvailableCourses() {
        return null;
    }
    
    /**
     * Get the current table's order based on selectedTableIndex
     */
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.request.order.Order getCurrentTableOrder() {
        return null;
    }
    
    /**
     * Get the current table ID, or null if no table is selected
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getCurrentTableId() {
        return null;
    }
    
    /**
     * Get cart items with courses for the current table
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.thedasagroup.suminative.ui.products.CartItemWithCourse> getCurrentTableCartItemsWithCourses() {
        return null;
    }
    
    /**
     * Get the current course filter for the current table
     */
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.products.CourseFilter getCurrentTableCourseFilter() {
        return null;
    }
    
    /**
     * Get filtered cart items based on current table's course filter
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.thedasagroup.suminative.ui.products.CartItemWithCourse> getFilteredCartItems() {
        return null;
    }
    
    /**
     * Get course counts for the current table
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<com.thedasagroup.suminative.ui.products.CourseFilter, java.lang.Integer> getCourseCounts() {
        return null;
    }
    
    public ProductsScreenState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.options_details.OptionDetails> component10() {
        return null;
    }
    
    public final double component11() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.request.pagination.OrderItem2 component12() {
        return null;
    }
    
    public final boolean component13() {
        return false;
    }
    
    public final boolean component14() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.sales.SalesReportResponse> component15() {
        return null;
    }
    
    public final boolean component16() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.request.sales.SalesRequest component17() {
        return null;
    }
    
    public final boolean component18() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.work.SyncStatus component19() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection> component20() {
        return null;
    }
    
    public final int component21() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.Integer, java.util.List<com.thedasagroup.suminative.ui.products.CartItemWithCourse>> component22() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.Integer, com.thedasagroup.suminative.ui.products.CourseFilter> component23() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.thedasagroup.suminative.ui.products.MealCourse> component24() {
        return null;
    }
    
    public final int component3() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.stock.StockItem component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.stock.StockItem component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.request.order.Order component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.Integer, com.thedasagroup.suminative.data.model.request.order.Order> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.order.OrderResponse2> component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.sales.SalesResponse> component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.products.ProductsScreenState copy(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> stockItemsResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> stockResponse, int stock, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.stock.StockItem showUpdateStockDialog, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.stock.StockItem isBottomSheetVisible, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, com.thedasagroup.suminative.data.model.request.order.Order> tableOrders, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.order.OrderResponse2> orderResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.sales.SalesResponse> salesResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.options_details.OptionDetails> optionDetailsResponse, double productTotal, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem2 isShowPrintingPreview, boolean shouldPrintInstant, boolean showCart, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.sales.SalesReportResponse> salesReportResponse, boolean showSalesReportDialog, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.sales.SalesRequest salesRequest, boolean refreshing, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.work.SyncStatus syncStatus, @org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection> selectedTables, int selectedTableIndex, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, ? extends java.util.List<com.thedasagroup.suminative.ui.products.CartItemWithCourse>> cartItemsWithCourses, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, ? extends com.thedasagroup.suminative.ui.products.CourseFilter> selectedCourseFilter, @org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.ui.products.MealCourse> availableCourses) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}