package com.thedasagroup.suminative.ui.refund;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = RefundSumUpActivity.class
)
@GeneratedEntryPoint
@InstallIn(ActivityComponent.class)
public interface RefundSumUpActivity_GeneratedInjector {
  void injectRefundSumUpActivity(RefundSumUpActivity refundSumUpActivity);
}
