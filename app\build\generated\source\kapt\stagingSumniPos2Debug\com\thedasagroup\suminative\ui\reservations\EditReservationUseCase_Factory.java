package com.thedasagroup.suminative.ui.reservations;

import com.thedasagroup.suminative.data.repo.ReservationsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class EditReservationUseCase_Factory implements Factory<EditReservationUseCase> {
  private final Provider<ReservationsRepository> reservationsRepositoryProvider;

  public EditReservationUseCase_Factory(
      Provider<ReservationsRepository> reservationsRepositoryProvider) {
    this.reservationsRepositoryProvider = reservationsRepositoryProvider;
  }

  @Override
  public EditReservationUseCase get() {
    return newInstance(reservationsRepositoryProvider.get());
  }

  public static EditReservationUseCase_Factory create(
      Provider<ReservationsRepository> reservationsRepositoryProvider) {
    return new EditReservationUseCase_Factory(reservationsRepositoryProvider);
  }

  public static EditReservationUseCase newInstance(ReservationsRepository reservationsRepository) {
    return new EditReservationUseCase(reservationsRepository);
  }
}
