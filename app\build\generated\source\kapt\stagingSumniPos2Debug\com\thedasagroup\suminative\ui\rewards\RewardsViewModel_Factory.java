package com.thedasagroup.suminative.ui.rewards;

import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.domain.rewards.AddPointsUseCase;
import com.thedasagroup.suminative.domain.rewards.GetAllCustomersUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RewardsViewModel_Factory {
  private final Provider<GetAllCustomersUseCase> getAllCustomersUseCaseProvider;

  private final Provider<AddPointsUseCase> addPointsUseCaseProvider;

  private final Provider<Prefs> prefsProvider;

  public RewardsViewModel_Factory(Provider<GetAllCustomersUseCase> getAllCustomersUseCaseProvider,
      Provider<AddPointsUseCase> addPointsUseCaseProvider, Provider<Prefs> prefsProvider) {
    this.getAllCustomersUseCaseProvider = getAllCustomersUseCaseProvider;
    this.addPointsUseCaseProvider = addPointsUseCaseProvider;
    this.prefsProvider = prefsProvider;
  }

  public RewardsViewModel get(RewardsState state) {
    return newInstance(state, getAllCustomersUseCaseProvider.get(), addPointsUseCaseProvider.get(), prefsProvider.get());
  }

  public static RewardsViewModel_Factory create(
      Provider<GetAllCustomersUseCase> getAllCustomersUseCaseProvider,
      Provider<AddPointsUseCase> addPointsUseCaseProvider, Provider<Prefs> prefsProvider) {
    return new RewardsViewModel_Factory(getAllCustomersUseCaseProvider, addPointsUseCaseProvider, prefsProvider);
  }

  public static RewardsViewModel newInstance(RewardsState state,
      GetAllCustomersUseCase getAllCustomersUseCase, AddPointsUseCase addPointsUseCase,
      Prefs prefs) {
    return new RewardsViewModel(state, getAllCustomersUseCase, addPointsUseCase, prefs);
  }
}
