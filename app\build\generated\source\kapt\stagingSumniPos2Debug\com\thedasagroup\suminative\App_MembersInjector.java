package com.thedasagroup.suminative;

import androidx.hilt.work.HiltWorkerFactory;
import com.instacart.truetime.time.TrueTimeImpl;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.work.OrderSyncManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class App_MembersInjector implements MembersInjector<App> {
  private final Provider<Prefs> prefsProvider;

  private final Provider<HourUtils> hourUtilsProvider;

  private final Provider<TrueTimeImpl> trueTimeProvider;

  private final Provider<HiltWorkerFactory> workerFactoryProvider;

  private final Provider<OrderSyncManager> orderSyncManagerProvider;

  public App_MembersInjector(Provider<Prefs> prefsProvider, Provider<HourUtils> hourUtilsProvider,
      Provider<TrueTimeImpl> trueTimeProvider, Provider<HiltWorkerFactory> workerFactoryProvider,
      Provider<OrderSyncManager> orderSyncManagerProvider) {
    this.prefsProvider = prefsProvider;
    this.hourUtilsProvider = hourUtilsProvider;
    this.trueTimeProvider = trueTimeProvider;
    this.workerFactoryProvider = workerFactoryProvider;
    this.orderSyncManagerProvider = orderSyncManagerProvider;
  }

  public static MembersInjector<App> create(Provider<Prefs> prefsProvider,
      Provider<HourUtils> hourUtilsProvider, Provider<TrueTimeImpl> trueTimeProvider,
      Provider<HiltWorkerFactory> workerFactoryProvider,
      Provider<OrderSyncManager> orderSyncManagerProvider) {
    return new App_MembersInjector(prefsProvider, hourUtilsProvider, trueTimeProvider, workerFactoryProvider, orderSyncManagerProvider);
  }

  @Override
  public void injectMembers(App instance) {
    injectPrefs(instance, prefsProvider.get());
    injectHourUtils(instance, hourUtilsProvider.get());
    injectTrueTime(instance, trueTimeProvider.get());
    injectWorkerFactory(instance, workerFactoryProvider.get());
    injectOrderSyncManager(instance, orderSyncManagerProvider.get());
  }

  @InjectedFieldSignature("com.thedasagroup.suminative.App.prefs")
  public static void injectPrefs(App instance, Prefs prefs) {
    instance.prefs = prefs;
  }

  @InjectedFieldSignature("com.thedasagroup.suminative.App.hourUtils")
  public static void injectHourUtils(App instance, HourUtils hourUtils) {
    instance.hourUtils = hourUtils;
  }

  @InjectedFieldSignature("com.thedasagroup.suminative.App.trueTime")
  public static void injectTrueTime(App instance, TrueTimeImpl trueTime) {
    instance.trueTime = trueTime;
  }

  @InjectedFieldSignature("com.thedasagroup.suminative.App.workerFactory")
  public static void injectWorkerFactory(App instance, HiltWorkerFactory workerFactory) {
    instance.workerFactory = workerFactory;
  }

  @InjectedFieldSignature("com.thedasagroup.suminative.App.orderSyncManager")
  public static void injectOrderSyncManager(App instance, OrderSyncManager orderSyncManager) {
    instance.orderSyncManager = orderSyncManager;
  }
}
