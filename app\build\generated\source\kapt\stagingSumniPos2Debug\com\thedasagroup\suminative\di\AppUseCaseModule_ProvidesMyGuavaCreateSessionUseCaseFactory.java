package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.MyGuavaRepository;
import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateSessionUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesMyGuavaCreateSessionUseCaseFactory implements Factory<MyGuavaCreateSessionUseCase> {
  private final Provider<MyGuavaRepository> myGuavaRepositoryProvider;

  public AppUseCaseModule_ProvidesMyGuavaCreateSessionUseCaseFactory(
      Provider<MyGuavaRepository> myGuavaRepositoryProvider) {
    this.myGuavaRepositoryProvider = myGuavaRepositoryProvider;
  }

  @Override
  public MyGuavaCreateSessionUseCase get() {
    return providesMyGuavaCreateSessionUseCase(myGuavaRepositoryProvider.get());
  }

  public static AppUseCaseModule_ProvidesMyGuavaCreateSessionUseCaseFactory create(
      Provider<MyGuavaRepository> myGuavaRepositoryProvider) {
    return new AppUseCaseModule_ProvidesMyGuavaCreateSessionUseCaseFactory(myGuavaRepositoryProvider);
  }

  public static MyGuavaCreateSessionUseCase providesMyGuavaCreateSessionUseCase(
      MyGuavaRepository myGuavaRepository) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesMyGuavaCreateSessionUseCase(myGuavaRepository));
  }
}
