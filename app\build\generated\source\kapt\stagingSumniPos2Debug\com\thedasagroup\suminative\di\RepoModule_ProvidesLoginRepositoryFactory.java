package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.LoginRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesLoginRepositoryFactory implements Factory<LoginRepository> {
  @Override
  public LoginRepository get() {
    return providesLoginRepository();
  }

  public static RepoModule_ProvidesLoginRepositoryFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static LoginRepository providesLoginRepository() {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesLoginRepository());
  }

  private static final class InstanceHolder {
    static final RepoModule_ProvidesLoginRepositoryFactory INSTANCE = new RepoModule_ProvidesLoginRepositoryFactory();
  }
}
