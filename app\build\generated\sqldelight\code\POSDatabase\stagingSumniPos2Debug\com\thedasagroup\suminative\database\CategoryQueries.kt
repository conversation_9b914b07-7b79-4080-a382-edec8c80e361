package com.thedasagroup.suminative.database

import app.cash.sqldelight.Query
import app.cash.sqldelight.TransacterImpl
import app.cash.sqldelight.db.QueryResult
import app.cash.sqldelight.db.SqlCursor
import app.cash.sqldelight.db.SqlDriver
import kotlin.Any
import kotlin.Long
import kotlin.String

public class CategoryQueries(
  driver: SqlDriver,
) : TransacterImpl(driver) {
  public fun <T : Any> getCategoriesByStore(storeId: Long, mapper: (
    id: Long,
    name: String,
    storeId: Long,
    sortOrder: Long,
    isActive: Long,
    createdAt: Long,
    updatedAt: Long,
    syncedAt: Long?,
  ) -> T): Query<T> = GetCategoriesByStoreQuery(storeId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2)!!,
      cursor.getLong(3)!!,
      cursor.getLong(4)!!,
      cursor.getLong(5)!!,
      cursor.getLong(6)!!,
      cursor.getLong(7)
    )
  }

  public fun getCategoriesByStore(storeId: Long): Query<CategoryEntity> =
      getCategoriesByStore(storeId) { id, name, storeId_, sortOrder, isActive, createdAt, updatedAt,
      syncedAt ->
    CategoryEntity(
      id,
      name,
      storeId_,
      sortOrder,
      isActive,
      createdAt,
      updatedAt,
      syncedAt
    )
  }

  public fun <T : Any> getAllCategoriesByStore(storeId: Long, mapper: (
    id: Long,
    name: String,
    storeId: Long,
    sortOrder: Long,
    isActive: Long,
    createdAt: Long,
    updatedAt: Long,
    syncedAt: Long?,
  ) -> T): Query<T> = GetAllCategoriesByStoreQuery(storeId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2)!!,
      cursor.getLong(3)!!,
      cursor.getLong(4)!!,
      cursor.getLong(5)!!,
      cursor.getLong(6)!!,
      cursor.getLong(7)
    )
  }

  public fun getAllCategoriesByStore(storeId: Long): Query<CategoryEntity> =
      getAllCategoriesByStore(storeId) { id, name, storeId_, sortOrder, isActive, createdAt,
      updatedAt, syncedAt ->
    CategoryEntity(
      id,
      name,
      storeId_,
      sortOrder,
      isActive,
      createdAt,
      updatedAt,
      syncedAt
    )
  }

  public fun <T : Any> getCategoryByNameAndStore(
    name: String,
    storeId: Long,
    mapper: (
      id: Long,
      name: String,
      storeId: Long,
      sortOrder: Long,
      isActive: Long,
      createdAt: Long,
      updatedAt: Long,
      syncedAt: Long?,
    ) -> T,
  ): Query<T> = GetCategoryByNameAndStoreQuery(name, storeId) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2)!!,
      cursor.getLong(3)!!,
      cursor.getLong(4)!!,
      cursor.getLong(5)!!,
      cursor.getLong(6)!!,
      cursor.getLong(7)
    )
  }

  public fun getCategoryByNameAndStore(name: String, storeId: Long): Query<CategoryEntity> =
      getCategoryByNameAndStore(name, storeId) { id, name_, storeId_, sortOrder, isActive,
      createdAt, updatedAt, syncedAt ->
    CategoryEntity(
      id,
      name_,
      storeId_,
      sortOrder,
      isActive,
      createdAt,
      updatedAt,
      syncedAt
    )
  }

  public fun <T : Any> getCategoryById(id: Long, mapper: (
    id: Long,
    name: String,
    storeId: Long,
    sortOrder: Long,
    isActive: Long,
    createdAt: Long,
    updatedAt: Long,
    syncedAt: Long?,
  ) -> T): Query<T> = GetCategoryByIdQuery(id) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2)!!,
      cursor.getLong(3)!!,
      cursor.getLong(4)!!,
      cursor.getLong(5)!!,
      cursor.getLong(6)!!,
      cursor.getLong(7)
    )
  }

  public fun getCategoryById(id: Long): Query<CategoryEntity> = getCategoryById(id) { id_, name,
      storeId, sortOrder, isActive, createdAt, updatedAt, syncedAt ->
    CategoryEntity(
      id_,
      name,
      storeId,
      sortOrder,
      isActive,
      createdAt,
      updatedAt,
      syncedAt
    )
  }

  public fun getCategoriesCountByStore(storeId: Long): Query<Long> =
      GetCategoriesCountByStoreQuery(storeId) { cursor ->
    cursor.getLong(0)!!
  }

  public fun getCategorySortOrderByName(name: String, storeId: Long): Query<Long> =
      GetCategorySortOrderByNameQuery(name, storeId) { cursor ->
    cursor.getLong(0)!!
  }

  public fun <T : Any> getUnsyncedCategories(mapper: (
    id: Long,
    name: String,
    storeId: Long,
    sortOrder: Long,
    isActive: Long,
    createdAt: Long,
    updatedAt: Long,
    syncedAt: Long?,
  ) -> T): Query<T> = Query(289_239_061, arrayOf("CategoryEntity"), driver, "Category.sq",
      "getUnsyncedCategories", """
  |SELECT CategoryEntity.id, CategoryEntity.name, CategoryEntity.storeId, CategoryEntity.sortOrder, CategoryEntity.isActive, CategoryEntity.createdAt, CategoryEntity.updatedAt, CategoryEntity.syncedAt FROM CategoryEntity
  |WHERE syncedAt IS NULL
  |ORDER BY createdAt ASC
  """.trimMargin()) { cursor ->
    mapper(
      cursor.getLong(0)!!,
      cursor.getString(1)!!,
      cursor.getLong(2)!!,
      cursor.getLong(3)!!,
      cursor.getLong(4)!!,
      cursor.getLong(5)!!,
      cursor.getLong(6)!!,
      cursor.getLong(7)
    )
  }

  public fun getUnsyncedCategories(): Query<CategoryEntity> = getUnsyncedCategories { id, name,
      storeId, sortOrder, isActive, createdAt, updatedAt, syncedAt ->
    CategoryEntity(
      id,
      name,
      storeId,
      sortOrder,
      isActive,
      createdAt,
      updatedAt,
      syncedAt
    )
  }

  /**
   * @return The number of rows updated.
   */
  public fun insertCategory(
    name: String,
    storeId: Long,
    sortOrder: Long,
    isActive: Long,
    createdAt: Long,
    updatedAt: Long,
  ): QueryResult<Long> {
    val result = driver.execute(-444_718_713, """
        |INSERT INTO CategoryEntity (
        |    name,
        |    storeId,
        |    sortOrder,
        |    isActive,
        |    createdAt,
        |    updatedAt
        |) VALUES (?, ?, ?, ?, ?, ?)
        """.trimMargin(), 6) {
          bindString(0, name)
          bindLong(1, storeId)
          bindLong(2, sortOrder)
          bindLong(3, isActive)
          bindLong(4, createdAt)
          bindLong(5, updatedAt)
        }
    notifyQueries(-444_718_713) { emit ->
      emit("CategoryEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun updateCategorySortOrder(
    sortOrder: Long,
    updatedAt: Long,
    id: Long,
  ): QueryResult<Long> {
    val result = driver.execute(1_796_515_449, """
        |UPDATE CategoryEntity
        |SET sortOrder = ?, updatedAt = ?
        |WHERE id = ?
        """.trimMargin(), 3) {
          bindLong(0, sortOrder)
          bindLong(1, updatedAt)
          bindLong(2, id)
        }
    notifyQueries(1_796_515_449) { emit ->
      emit("CategoryEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun updateCategoryActiveStatus(
    isActive: Long,
    updatedAt: Long,
    id: Long,
  ): QueryResult<Long> {
    val result = driver.execute(602_705_871, """
        |UPDATE CategoryEntity
        |SET isActive = ?, updatedAt = ?
        |WHERE id = ?
        """.trimMargin(), 3) {
          bindLong(0, isActive)
          bindLong(1, updatedAt)
          bindLong(2, id)
        }
    notifyQueries(602_705_871) { emit ->
      emit("CategoryEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun updateCategorySyncedAt(
    syncedAt: Long?,
    updatedAt: Long,
    id: Long,
  ): QueryResult<Long> {
    val result = driver.execute(1_737_499_396, """
        |UPDATE CategoryEntity
        |SET syncedAt = ?, updatedAt = ?
        |WHERE id = ?
        """.trimMargin(), 3) {
          bindLong(0, syncedAt)
          bindLong(1, updatedAt)
          bindLong(2, id)
        }
    notifyQueries(1_737_499_396) { emit ->
      emit("CategoryEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun deleteCategoryById(id: Long): QueryResult<Long> {
    val result = driver.execute(-1_051_542_037, """DELETE FROM CategoryEntity WHERE id = ?""", 1) {
          bindLong(0, id)
        }
    notifyQueries(-1_051_542_037) { emit ->
      emit("CategoryEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun deleteCategoriesByStore(storeId: Long): QueryResult<Long> {
    val result = driver.execute(-1_632_798_221, """DELETE FROM CategoryEntity WHERE storeId = ?""",
        1) {
          bindLong(0, storeId)
        }
    notifyQueries(-1_632_798_221) { emit ->
      emit("CategoryEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun updateCategoriesSortOrder(
    sortOrder: Long,
    updatedAt: Long,
    name: String,
    storeId: Long,
  ): QueryResult<Long> {
    val result = driver.execute(146_225_499, """
        |UPDATE CategoryEntity
        |SET sortOrder = ?, updatedAt = ?
        |WHERE name = ? AND storeId = ?
        """.trimMargin(), 4) {
          bindLong(0, sortOrder)
          bindLong(1, updatedAt)
          bindString(2, name)
          bindLong(3, storeId)
        }
    notifyQueries(146_225_499) { emit ->
      emit("CategoryEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun insertOrReplaceCategory(
    name: String,
    storeId: Long,
    sortOrder: Long,
    isActive: Long,
    createdAt: Long,
    updatedAt: Long,
  ): QueryResult<Long> {
    val result = driver.execute(1_979_141_382, """
        |INSERT OR REPLACE INTO CategoryEntity (
        |    name,
        |    storeId,
        |    sortOrder,
        |    isActive,
        |    createdAt,
        |    updatedAt
        |) VALUES (?, ?, ?, ?, ?, ?)
        """.trimMargin(), 6) {
          bindString(0, name)
          bindLong(1, storeId)
          bindLong(2, sortOrder)
          bindLong(3, isActive)
          bindLong(4, createdAt)
          bindLong(5, updatedAt)
        }
    notifyQueries(1_979_141_382) { emit ->
      emit("CategoryEntity")
    }
    return result
  }

  /**
   * @return The number of rows updated.
   */
  public fun markCategoriesSynced(
    syncedAt: Long?,
    updatedAt: Long,
    storeId: Long,
  ): QueryResult<Long> {
    val result = driver.execute(-1_847_300_781, """
        |UPDATE CategoryEntity
        |SET syncedAt = ?, updatedAt = ?
        |WHERE storeId = ?
        """.trimMargin(), 3) {
          bindLong(0, syncedAt)
          bindLong(1, updatedAt)
          bindLong(2, storeId)
        }
    notifyQueries(-1_847_300_781) { emit ->
      emit("CategoryEntity")
    }
    return result
  }

  private inner class GetCategoriesByStoreQuery<out T : Any>(
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("CategoryEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("CategoryEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-430_219_512, """
    |SELECT CategoryEntity.id, CategoryEntity.name, CategoryEntity.storeId, CategoryEntity.sortOrder, CategoryEntity.isActive, CategoryEntity.createdAt, CategoryEntity.updatedAt, CategoryEntity.syncedAt FROM CategoryEntity
    |WHERE storeId = ? AND isActive = 1
    |ORDER BY sortOrder ASC, name ASC
    """.trimMargin(), mapper, 1) {
      bindLong(0, storeId)
    }

    override fun toString(): String = "Category.sq:getCategoriesByStore"
  }

  private inner class GetAllCategoriesByStoreQuery<out T : Any>(
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("CategoryEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("CategoryEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(1_106_373_939, """
    |SELECT CategoryEntity.id, CategoryEntity.name, CategoryEntity.storeId, CategoryEntity.sortOrder, CategoryEntity.isActive, CategoryEntity.createdAt, CategoryEntity.updatedAt, CategoryEntity.syncedAt FROM CategoryEntity
    |WHERE storeId = ?
    |ORDER BY sortOrder ASC, name ASC
    """.trimMargin(), mapper, 1) {
      bindLong(0, storeId)
    }

    override fun toString(): String = "Category.sq:getAllCategoriesByStore"
  }

  private inner class GetCategoryByNameAndStoreQuery<out T : Any>(
    public val name: String,
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("CategoryEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("CategoryEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(1_667_182_032, """
    |SELECT CategoryEntity.id, CategoryEntity.name, CategoryEntity.storeId, CategoryEntity.sortOrder, CategoryEntity.isActive, CategoryEntity.createdAt, CategoryEntity.updatedAt, CategoryEntity.syncedAt FROM CategoryEntity
    |WHERE name = ? AND storeId = ? LIMIT 1
    """.trimMargin(), mapper, 2) {
      bindString(0, name)
      bindLong(1, storeId)
    }

    override fun toString(): String = "Category.sq:getCategoryByNameAndStore"
  }

  private inner class GetCategoryByIdQuery<out T : Any>(
    public val id: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("CategoryEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("CategoryEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(1_517_633_462, """
    |SELECT CategoryEntity.id, CategoryEntity.name, CategoryEntity.storeId, CategoryEntity.sortOrder, CategoryEntity.isActive, CategoryEntity.createdAt, CategoryEntity.updatedAt, CategoryEntity.syncedAt FROM CategoryEntity
    |WHERE id = ? LIMIT 1
    """.trimMargin(), mapper, 1) {
      bindLong(0, id)
    }

    override fun toString(): String = "Category.sq:getCategoryById"
  }

  private inner class GetCategoriesCountByStoreQuery<out T : Any>(
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("CategoryEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("CategoryEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(1_989_907_773, """
    |SELECT COUNT(*) FROM CategoryEntity
    |WHERE storeId = ? AND isActive = 1
    """.trimMargin(), mapper, 1) {
      bindLong(0, storeId)
    }

    override fun toString(): String = "Category.sq:getCategoriesCountByStore"
  }

  private inner class GetCategorySortOrderByNameQuery<out T : Any>(
    public val name: String,
    public val storeId: Long,
    mapper: (SqlCursor) -> T,
  ) : Query<T>(mapper) {
    override fun addListener(listener: Query.Listener) {
      driver.addListener("CategoryEntity", listener = listener)
    }

    override fun removeListener(listener: Query.Listener) {
      driver.removeListener("CategoryEntity", listener = listener)
    }

    override fun <R> execute(mapper: (SqlCursor) -> QueryResult<R>): QueryResult<R> =
        driver.executeQuery(-1_523_228_274, """
    |SELECT sortOrder FROM CategoryEntity
    |WHERE name = ? AND storeId = ? AND isActive = 1
    """.trimMargin(), mapper, 2) {
      bindString(0, name)
      bindLong(1, storeId)
    }

    override fun toString(): String = "Category.sq:getCategorySortOrderByName"
  }
}
