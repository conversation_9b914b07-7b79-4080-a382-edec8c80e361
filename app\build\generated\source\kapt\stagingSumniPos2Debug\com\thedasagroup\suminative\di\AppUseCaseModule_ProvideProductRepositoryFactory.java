package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.database.DatabaseManager;
import com.thedasagroup.suminative.data.repo.ProductRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvideProductRepositoryFactory implements Factory<ProductRepository> {
  private final Provider<DatabaseManager> databaseManagerProvider;

  public AppUseCaseModule_ProvideProductRepositoryFactory(
      Provider<DatabaseManager> databaseManagerProvider) {
    this.databaseManagerProvider = databaseManagerProvider;
  }

  @Override
  public ProductRepository get() {
    return provideProductRepository(databaseManagerProvider.get());
  }

  public static AppUseCaseModule_ProvideProductRepositoryFactory create(
      Provider<DatabaseManager> databaseManagerProvider) {
    return new AppUseCaseModule_ProvideProductRepositoryFactory(databaseManagerProvider);
  }

  public static ProductRepository provideProductRepository(DatabaseManager databaseManager) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.provideProductRepository(databaseManager));
  }
}
