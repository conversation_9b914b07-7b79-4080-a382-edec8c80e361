package com.thedasagroup.suminative.database

import app.cash.sqldelight.Transacter
import app.cash.sqldelight.db.QueryResult
import app.cash.sqldelight.db.SqlDriver
import app.cash.sqldelight.db.SqlSchema
import com.thedasagroup.suminative.database.app.newInstance
import com.thedasagroup.suminative.database.app.schema
import kotlin.Unit

public interface POSDatabase : Transacter {
  public val categoryQueries: CategoryQueries

  public val optionQueries: OptionQueries

  public val optionSetQueries: OptionSetQueries

  public val orderQueries: OrderQueries

  public val orderItemQueries: OrderItemQueries

  public val productQueries: ProductQueries

  public companion object {
    public val Schema: SqlSchema<QueryResult.Value<Unit>>
      get() = POSDatabase::class.schema

    public operator fun invoke(driver: SqlDriver): POSDatabase =
        POSDatabase::class.newInstance(driver)
  }
}
