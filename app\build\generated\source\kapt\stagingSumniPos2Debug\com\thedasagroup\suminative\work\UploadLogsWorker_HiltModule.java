package com.thedasagroup.suminative.work;

import androidx.hilt.work.WorkerAssistedFactory;
import androidx.work.ListenableWorker;
import dagger.Binds;
import dagger.Module;
import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.multibindings.IntoMap;
import dagger.multibindings.StringKey;

@Module
@InstallIn(SingletonComponent.class)
@OriginatingElement(
    topLevelClass = UploadLogsWorker.class
)
public interface UploadLogsWorker_HiltModule {
  @Binds
  @IntoMap
  @StringKey("com.thedasagroup.suminative.work.UploadLogsWorker")
  WorkerAssistedFactory<? extends ListenableWorker> bind(UploadLogsWorker_AssistedFactory factory);
}
