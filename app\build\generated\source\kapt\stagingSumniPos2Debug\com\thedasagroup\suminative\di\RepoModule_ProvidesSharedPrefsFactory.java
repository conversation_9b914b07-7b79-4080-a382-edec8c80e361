package com.thedasagroup.suminative.di;

import android.content.Context;
import com.thedasagroup.suminative.data.prefs.Prefs;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesSharedPrefsFactory implements Factory<Prefs> {
  private final Provider<Context> contextProvider;

  public RepoModule_ProvidesSharedPrefsFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public Prefs get() {
    return providesSharedPrefs(contextProvider.get());
  }

  public static RepoModule_ProvidesSharedPrefsFactory create(Provider<Context> contextProvider) {
    return new RepoModule_ProvidesSharedPrefsFactory(contextProvider);
  }

  public static Prefs providesSharedPrefs(Context context) {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesSharedPrefs(context));
  }
}
