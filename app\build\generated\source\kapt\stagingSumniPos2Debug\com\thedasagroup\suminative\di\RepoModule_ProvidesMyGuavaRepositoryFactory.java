package com.thedasagroup.suminative.di;

import com.instacart.truetime.time.TrueTimeImpl;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.MyGuavaRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesMyGuavaRepositoryFactory implements Factory<MyGuavaRepository> {
  private final Provider<TrueTimeImpl> trueTimeImplProvider;

  private final Provider<Prefs> prefsProvider;

  public RepoModule_ProvidesMyGuavaRepositoryFactory(Provider<TrueTimeImpl> trueTimeImplProvider,
      Provider<Prefs> prefsProvider) {
    this.trueTimeImplProvider = trueTimeImplProvider;
    this.prefsProvider = prefsProvider;
  }

  @Override
  public MyGuavaRepository get() {
    return providesMyGuavaRepository(trueTimeImplProvider.get(), prefsProvider.get());
  }

  public static RepoModule_ProvidesMyGuavaRepositoryFactory create(
      Provider<TrueTimeImpl> trueTimeImplProvider, Provider<Prefs> prefsProvider) {
    return new RepoModule_ProvidesMyGuavaRepositoryFactory(trueTimeImplProvider, prefsProvider);
  }

  public static MyGuavaRepository providesMyGuavaRepository(TrueTimeImpl trueTimeImpl,
      Prefs prefs) {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesMyGuavaRepository(trueTimeImpl, prefs));
  }
}
