package com.thedasagroup.suminative.ui.rewards;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000<\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\u001a(\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00040\b2\b\b\u0002\u0010\t\u001a\u00020\nH\u0007\u001a\u001e\u0010\u000b\u001a\u00020\u00042\u0006\u0010\f\u001a\u00020\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00040\bH\u0007\u001a\u0018\u0010\u000f\u001a\u00020\u00042\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0011H\u0007\u001a\u0017\u0010\u0013\u001a\u0004\u0018\u00010\u00142\u0006\u0010\u0015\u001a\u00020\u0011H\u0002\u00a2\u0006\u0002\u0010\u0016\"\u0010\u0010\u0000\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0004\n\u0002\u0010\u0002\u00a8\u0006\u0017"}, d2 = {"ThemeGreen", "Landroidx/compose/ui/graphics/Color;", "J", "RewardsScreen", "", "viewModel", "Lcom/thedasagroup/suminative/ui/rewards/RewardsViewModel;", "onBackClick", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "CustomerInfoCard", "customer", "Lcom/thedasagroup/suminative/data/model/response/rewards/RewardsCustomer;", "onAddPointsClick", "CustomerInfoRow", "label", "", "value", "extractCustomerId", "", "input", "(Ljava/lang/String;)Ljava/lang/Integer;", "app_stagingSumniPos2Debug"})
public final class RewardsScreenKt {
    private static final long ThemeGreen = 0L;
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void RewardsScreen(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.rewards.RewardsViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CustomerInfoCard(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer customer, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onAddPointsClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CustomerInfoRow(@org.jetbrains.annotations.NotNull()
    java.lang.String label, @org.jetbrains.annotations.NotNull()
    java.lang.String value) {
    }
    
    private static final java.lang.Integer extractCustomerId(java.lang.String input) {
        return null;
    }
}