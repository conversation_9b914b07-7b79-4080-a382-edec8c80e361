package com.thedasagroup.suminative.di;

import android.content.Context;
import com.thedasagroup.suminative.ui.utils.SoundPoolPlayer;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesSoundPoolPlayerFactory implements Factory<SoundPoolPlayer> {
  private final Provider<Context> contextProvider;

  public RepoModule_ProvidesSoundPoolPlayerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public SoundPoolPlayer get() {
    return providesSoundPoolPlayer(contextProvider.get());
  }

  public static RepoModule_ProvidesSoundPoolPlayerFactory create(
      Provider<Context> contextProvider) {
    return new RepoModule_ProvidesSoundPoolPlayerFactory(contextProvider);
  }

  public static SoundPoolPlayer providesSoundPoolPlayer(Context context) {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesSoundPoolPlayer(context));
  }
}
