package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.RewardsRepository;
import com.thedasagroup.suminative.domain.rewards.AddPointsUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesAddPointsUseCaseFactory implements Factory<AddPointsUseCase> {
  private final Provider<RewardsRepository> rewardsRepositoryProvider;

  public AppUseCaseModule_ProvidesAddPointsUseCaseFactory(
      Provider<RewardsRepository> rewardsRepositoryProvider) {
    this.rewardsRepositoryProvider = rewardsRepositoryProvider;
  }

  @Override
  public AddPointsUseCase get() {
    return providesAddPointsUseCase(rewardsRepositoryProvider.get());
  }

  public static AppUseCaseModule_ProvidesAddPointsUseCaseFactory create(
      Provider<RewardsRepository> rewardsRepositoryProvider) {
    return new AppUseCaseModule_ProvidesAddPointsUseCaseFactory(rewardsRepositoryProvider);
  }

  public static AddPointsUseCase providesAddPointsUseCase(RewardsRepository rewardsRepository) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesAddPointsUseCase(rewardsRepository));
  }
}
